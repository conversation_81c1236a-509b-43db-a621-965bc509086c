# Campi per Importazione Excel - Sistema CMS

Questo documento elenca **esattamente** i campi richiesti per l'importazione Excel dei cavi.

## 📋 **CAMPI OBBLIGATORI per Importazione Cavi**

### **<PERSON>on<PERSON> (case-insensitive)**

| Campo | Nome Colonna | Tipo | Obbligatorio | Descrizione |
|-------|--------------|------|--------------|-------------|
| **ID Cavo** | `id_cavo` | Testo | ✅ **SÌ** | Identificativo univoco del cavo |
| **Utility** | `utility` | Testo | ✅ **SÌ** | Tipo di utility (es. "Energia", "Telecom") |
| **Tipologia** | `tipologia` | Testo | ✅ **SÌ** | Tipologia del cavo (es. "MT", "BT") |
| **Metri Teorici** | `metri_teorici` | Numero | ✅ **SÌ** | Lunghezza teorica del cavo |

### **Colonne Opzionali (ma Consigliate)**

| Campo | Nome Colonna | Tipo | Descrizione |
|-------|--------------|------|-------------|
| **Sistema** | `sistema` | Testo | Sistema di appartenenza |
| **Colore Cavo** | `colore_cavo` | Testo | Colore del cavo |
| **Ubicazione Partenza** | `ubicazione_partenza` | Testo | Punto di partenza |
| **Utenza Partenza** | `utenza_partenza` | Testo | Utenza di partenza |
| **Descrizione Utenza Partenza** | `descrizione_utenza_partenza` | Testo | Descrizione dettagliata |
| **Ubicazione Arrivo** | `ubicazione_arrivo` | Testo | Punto di arrivo |
| **Utenza Arrivo** | `utenza_arrivo` | Testo | Utenza di arrivo |
| **Descrizione Utenza Arrivo** | `descrizione_utenza_arrivo` | Testo | Descrizione dettagliata |
| **Responsabile Posa** | `responsabile_posa` | Testo | Chi è responsabile della posa |

## 🔧 **Regole di Validazione**

### **1. Campo "formazione" vs "sezione"**
- ✅ **Entrambi accettati**: Puoi usare `formazione` O `sezione`
- ✅ **Mapping automatico**: `formazione` viene automaticamente mappato a `sezione`
- ✅ **Esempi validi**: "3x2.5", "4x1.5", "2x4", "1x10"

### **2. Campo "n_conduttori" (Derivato Automaticamente)**
- ✅ **Non obbligatorio**: Viene derivato automaticamente da `formazione`
- ✅ **Estrazione automatica**: Da "3x2.5" estrae "3"
- ✅ **Se presente**: Viene mantenuto il valore specificato

### **3. Campi con Valori Predefiniti**
Se non specificati, vengono impostati automaticamente a "TBD":
- `sistema`
- `colore_cavo`
- `ubicazione_partenza`
- `utenza_partenza`
- `descrizione_utenza_partenza`
- `ubicazione_arrivo`
- `utenza_arrivo`
- `descrizione_utenza_arrivo`
- `responsabile_posa`

## 📊 **Esempio di File Excel Corretto**

### **Versione Minima (Solo Campi Obbligatori)**
```
id_cavo     | utility | tipologia | formazione | metri_teorici
C001        | Energia | MT        | 3x2.5      | 100
C002        | Telecom | BT        | 4x1.5      | 150
C003        | Energia | MT        | 2x4        | 200
```

### **Versione Completa (Con Campi Opzionali)**
```
id_cavo | utility | tipologia | formazione | metri_teorici | sistema | ubicazione_partenza | ubicazione_arrivo
C001    | Energia | MT        | 3x2.5      | 100          | SIS1    | Cabina A           | Cabina B
C002    | Telecom | BT        | 4x1.5      | 150          | SIS2    | Palo 1             | Palo 2
```

## ⚠️ **Errori Comuni da Evitare**

### **1. Nomi Colonne Errati**
❌ **SBAGLIATO**: `ID_Cavo`, `UTILITY`, `Tipologia_Cavo`
✅ **CORRETTO**: `id_cavo`, `utility`, `tipologia`

### **2. Campi Vuoti Obbligatori**
❌ **SBAGLIATO**: Lasciare vuoti `id_cavo`, `utility`, `tipologia`, `formazione`, `metri_teorici`
✅ **CORRETTO**: Compilare tutti i campi obbligatori

### **3. Formato Metri Teorici**
❌ **SBAGLIATO**: "100m", "150 metri", "duecento"
✅ **CORRETTO**: 100, 150, 200 (solo numeri)

### **4. Formato Formazione**
❌ **SBAGLIATO**: "3 x 2.5", "tre per due e mezzo"
✅ **CORRETTO**: "3x2.5", "4x1.5", "2x4"

## 🛠️ **Come Creare un Template Corretto**

### **Opzione 1: Scarica Template dalla Webapp**
1. Vai su **Gestione Excel** nella webapp
2. Clicca **"Crea Template Excel per cavi"**
3. Scarica il file generato automaticamente

### **Opzione 2: Crea Manualmente**
1. Apri Excel
2. Nella prima riga inserisci le intestazioni:
   ```
   id_cavo | utility | tipologia | formazione | metri_teorici
   ```
3. Compila i dati nelle righe successive

## 🔍 **Debug Errori di Importazione**

### **Errore: "Colonne obbligatorie mancanti"**
- ✅ Verifica che tutte le 5 colonne obbligatorie siano presenti
- ✅ Controlla che i nomi siano scritti correttamente (minuscolo, underscore)

### **Errore: "File vuoto o non valido"**
- ✅ Verifica che il file contenga dati (non solo intestazioni)
- ✅ Controlla che il formato sia .xlsx o .xls

### **Errore: "Nessun dato valido da importare"**
- ✅ Verifica che almeno una riga abbia tutti i campi obbligatori compilati
- ✅ Controlla che `metri_teorici` sia un numero valido

## 📞 **Supporto**

Se continui ad avere problemi:
1. Verifica che il file rispetti esattamente questa struttura
2. Usa il template generato dalla webapp
3. Controlla i log del backend per errori specifici

---

**Nota**: I nomi delle colonne sono **case-insensitive** (maiuscolo/minuscolo non importa) e gli spazi vengono automaticamente convertiti in underscore.
