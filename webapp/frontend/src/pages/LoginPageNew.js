import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Card,
  CardContent,
  CardActions,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  AdminPanelSettings as AdminIcon,
  Person as UserIcon,
  Construction as ConstructionIcon,
  Password as PasswordIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const LoginPageNew = () => {
  const [loginType, setLoginType] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);
  const [recoveryEmail, setRecoveryEmail] = useState('');

  // Credenziali per i diversi tipi di login
  const [adminCredentials, setAdminCredentials] = useState({
    username: '',
    password: ''
  });

  const [userCredentials, setUserCredentials] = useState({
    username: '',
    password: ''
  });

  const [cantiereCredentials, setCantiereCredentials] = useState({
    codice_univoco: '',
    password: ''
  });

  const { login } = useAuth();
  const navigate = useNavigate();

  // Gestione dell'input per il login amministratore
  const handleAdminInputChange = (e) => {
    const { name, value } = e.target;
    setAdminCredentials({
      ...adminCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il login utente standard
  const handleUserInputChange = (e) => {
    const { name, value } = e.target;
    setUserCredentials({
      ...userCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il login cantiere
  const handleCantiereInputChange = (e) => {
    const { name, value } = e.target;
    setCantiereCredentials({
      ...cantiereCredentials,
      [name]: value
    });
  };

  // Gestione dell'input per il recupero password
  const handleRecoveryEmailChange = (e) => {
    setRecoveryEmail(e.target.value);
  };

  // Gestione del login amministratore
  const handleAdminLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login amministratore con:', adminCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!adminCredentials.username || !adminCredentials.password) {
        console.log('Campi vuoti nel form di login admin');
        setError('Username e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per admin...');
      try {
        const userData = await login(adminCredentials, 'standard');
        console.log('Login admin completato, dati utente:', userData);

        // Verifica che l'utente sia un amministratore
        if (userData.role !== 'owner') {
          console.log('Utente non ha ruolo owner:', userData.role);
          setError('Non hai i permessi di amministratore');
          setLoading(false);
          return;
        }

        // Reindirizza alla dashboard di amministrazione
        console.log('Reindirizzamento a /dashboard/admin');
        // Utilizza navigate invece di window.location per evitare refresh completo
        navigate('/dashboard/admin');
      } catch (loginError) {
        console.error('Errore specifico durante login admin:', loginError);
        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Errore generale durante login admin:', err);
      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del login utente standard
  const handleUserLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login utente standard con:', userCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!userCredentials.username || !userCredentials.password) {
        console.log('Campi vuoti nel form di login utente standard');
        setError('Username e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per utente standard...');
      const userData = await login(userCredentials, 'standard');
      console.log('Login utente standard completato, dati utente:', userData);

      // Verifica che l'utente sia un utente standard
      if (userData.role !== 'user') {
        console.log('Utente non ha ruolo user:', userData.role);
        setError('Non hai i permessi di utente standard');
        setLoading(false);
        return;
      }

      // Reindirizza alla dashboard utente
      console.log('Reindirizzamento a /dashboard/cantieri');
      // Utilizza navigate invece di window.location per evitare refresh completo
      navigate('/dashboard/cantieri');
    } catch (err) {
      console.error('Errore durante login utente standard:', err);
      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del login cantiere
  const handleCantiereLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    console.log('Tentativo di login cantiere con:', cantiereCredentials);

    try {
      // Verifica che i campi non siano vuoti
      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {
        console.log('Campi vuoti nel form di login cantiere');
        setError('Codice univoco e password non possono essere vuoti');
        setLoading(false);
        return;
      }

      console.log('Chiamata alla funzione login per cantiere...');
      try {
        const userData = await login(cantiereCredentials, 'cantiere');
        console.log('Login cantiere completato, dati utente:', userData);

        // Salva esplicitamente l'ID e il nome del cantiere nel localStorage
        if (userData.cantiere_id) {
          console.log('Salvando ID cantiere nel localStorage:', userData.cantiere_id);
          localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString());
          localStorage.setItem('selectedCantiereName', userData.cantiere_name || `Cantiere ${userData.cantiere_id}`);
        } else {
          console.warn('Risposta login cantiere non contiene cantiere_id:', userData);
        }

        // Breve ritardo per assicurarsi che i dati siano salvati nel localStorage
        setTimeout(() => {
          // Reindirizza alla pagina di visualizzazione cavi
          console.log('Reindirizzamento a /dashboard/cavi/visualizza');
          // Utilizza navigate invece di window.location per evitare refresh completo
          navigate('/dashboard/cavi/visualizza');
        }, 500);
      } catch (loginError) {
        console.error('Errore specifico durante login cantiere:', loginError);
        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Errore generale durante login cantiere:', err);
      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');
      setLoading(false);
    }
    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione
  };

  // Gestione del recupero password
  const handlePasswordRecovery = () => {
    // Qui implementeremo la logica per il recupero della password
    alert('Funzionalità di recupero password non ancora implementata');
    setShowPasswordRecoveryDialog(false);
  };

  // Torna al menu principale
  const handleBackToMainMenu = () => {
    setLoginType(null);
    setError('');
  };

  // Renderizza il form di login appropriato in base al tipo selezionato
  const renderLoginForm = () => {
    switch (loginType) {
      case 'admin':
        return (
          <Box component="form" onSubmit={handleAdminLogin} noValidate>
            <Typography variant="h5" gutterBottom>
              Login Amministratore
            </Typography>
            <TextField
              margin="normal"
              required
              fullWidth
              id="admin-username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={adminCredentials.username}
              onChange={handleAdminInputChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="admin-password"
              autoComplete="current-password"
              value={adminCredentials.password}
              onChange={handleAdminInputChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{ mb: 2 }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      case 'user':
        return (
          <Box component="form" onSubmit={handleUserLogin} noValidate>
            <Typography variant="h5" gutterBottom>
              Login Utente Standard
            </Typography>
            <TextField
              margin="normal"
              required
              fullWidth
              id="user-username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={userCredentials.username}
              onChange={handleUserInputChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="user-password"
              autoComplete="current-password"
              value={userCredentials.password}
              onChange={handleUserInputChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{ mb: 2 }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      case 'cantiere':
        return (
          <Box component="form" onSubmit={handleCantiereLogin} noValidate>
            <Typography variant="h5" gutterBottom>
              Login Utente Cantiere
            </Typography>
            <TextField
              margin="normal"
              required
              fullWidth
              id="codice_univoco"
              label="Codice Univoco del Cantiere"
              name="codice_univoco"
              autoComplete="off"
              autoFocus
              value={cantiereCredentials.codice_univoco}
              onChange={handleCantiereInputChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="cantiere-password"
              autoComplete="current-password"
              value={cantiereCredentials.password}
              onChange={handleCantiereInputChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Accedi'}
            </Button>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleBackToMainMenu}
              sx={{ mb: 2 }}
            >
              Torna al Menu Principale
            </Button>
          </Box>
        );
      default:
        return null;
    }
  };

  // Renderizza il menu principale di login
  const renderMainMenu = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AdminIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
              <Typography variant="h5" component="div" gutterBottom>
                Login Amministratore
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Accedi come amministratore per gestire utenti e sistema
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="contained"
                onClick={() => setLoginType('admin')}
              >
                Accedi
              </Button>
            </CardActions>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <UserIcon sx={{ fontSize: 60, color: 'secondary.main', mb: 2 }} />
              <Typography variant="h5" component="div" gutterBottom>
                Login Utente Standard
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Accedi come utente standard per gestire i tuoi cantieri
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="contained"
                color="secondary"
                onClick={() => setLoginType('user')}
              >
                Accedi
              </Button>
            </CardActions>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ConstructionIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
              <Typography variant="h5" component="div" gutterBottom>
                Login Utente Cantiere
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Accedi direttamente a un cantiere specifico
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="contained"
                color="success"
                onClick={() => setLoginType('cantiere')}
              >
                Accedi
              </Button>
            </CardActions>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PasswordIcon sx={{ fontSize: 60, color: 'warning.main', mb: 2 }} />
              <Typography variant="h5" component="div" gutterBottom>
                Recupero Password Admin
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Recupera la password dell'amministratore
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="contained"
                color="warning"
                onClick={() => setShowPasswordRecoveryDialog(true)}
              >
                Recupera
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container component="main" maxWidth="md">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" sx={{ mb: 4 }}>
          Sistema di Gestione Cantieri
        </Typography>

        <Paper elevation={3} sx={{ width: '100%', p: 3 }}>
          <Typography variant="h5" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>
            {loginType ? 'Login' : 'Seleziona Tipo di Accesso'}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {loginType ? renderLoginForm() : renderMainMenu()}
        </Paper>
      </Box>

      {/* Dialog per il recupero password */}
      <Dialog open={showPasswordRecoveryDialog} onClose={() => setShowPasswordRecoveryDialog(false)}>
        <DialogTitle>Recupero Password Amministratore</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="email"
            label="Indirizzo Email"
            type="email"
            fullWidth
            variant="outlined"
            value={recoveryEmail}
            onChange={handleRecoveryEmailChange}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPasswordRecoveryDialog(false)}>Annulla</Button>
          <Button onClick={handlePasswordRecovery} variant="contained">Invia</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default LoginPageNew;
