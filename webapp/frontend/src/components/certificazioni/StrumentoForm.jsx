import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  Alert
} from '@mui/material';
import { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';

import { apiService } from '../../services/apiService';

function StrumentoForm({ cantiereId, strumento, onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    nome: '',
    marca: '',
    modello: '',
    numero_serie: '',
    data_calibrazione: null,
    data_scadenza_calibrazione: null,
    certificato_calibrazione: '',
    note: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (strumento) {
      setFormData({
        nome: strumento.nome || '',
        marca: strumento.marca || '',
        modello: strumento.modello || '',
        numero_serie: strumento.numero_serie || '',
        data_calibrazione: strumento.data_calibrazione ? new Date(strumento.data_calibrazione) : null,
        data_scadenza_calibrazione: strumento.data_scadenza_calibrazione ? new Date(strumento.data_scadenza_calibrazione) : null,
        certificato_calibrazione: strumento.certificato_calibrazione || '',
        note: strumento.note || ''
      });
    }
  }, [strumento]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (field, date) => {
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));
  };

  const validateForm = () => {
    if (!formData.nome.trim()) {
      setError('Il nome dello strumento è obbligatorio');
      return false;
    }
    if (!formData.marca.trim()) {
      setError('La marca dello strumento è obbligatoria');
      return false;
    }
    if (!formData.modello.trim()) {
      setError('Il modello dello strumento è obbligatorio');
      return false;
    }
    if (!formData.numero_serie.trim()) {
      setError('Il numero di serie è obbligatorio');
      return false;
    }
    if (!formData.data_calibrazione) {
      setError('La data di calibrazione è obbligatoria');
      return false;
    }
    if (!formData.data_scadenza_calibrazione) {
      setError('La data di scadenza calibrazione è obbligatoria');
      return false;
    }
    if (formData.data_scadenza_calibrazione <= formData.data_calibrazione) {
      setError('La data di scadenza deve essere successiva alla data di calibrazione');
      return false;
    }
    return true;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError('');

      const submitData = {
        nome: formData.nome.trim(),
        marca: formData.marca.trim(),
        modello: formData.modello.trim(),
        numero_serie: formData.numero_serie.trim(),
        data_calibrazione: formData.data_calibrazione.toISOString().split('T')[0],
        data_scadenza_calibrazione: formData.data_scadenza_calibrazione.toISOString().split('T')[0],
        certificato_calibrazione: formData.certificato_calibrazione.trim() || null,
        note: formData.note.trim() || null
      };

      if (strumento) {
        await apiService.updateStrumento(cantiereId, strumento.id_strumento, submitData);
        onSuccess('Strumento aggiornato con successo');
      } else {
        await apiService.createStrumento(cantiereId, submitData);
        onSuccess('Strumento creato con successo');
      }
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.response?.data?.detail || 'Errore nel salvataggio dello strumento');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {strumento ? 'Modifica Strumento' : 'Nuovo Strumento'}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Informazioni Base */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Informazioni Strumento
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Nome Strumento"
                value={formData.nome}
                onChange={(e) => handleInputChange('nome', e.target.value)}
                fullWidth
                required
                placeholder="es. Multimetro, Tester di isolamento, ecc."
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Marca"
                value={formData.marca}
                onChange={(e) => handleInputChange('marca', e.target.value)}
                fullWidth
                required
                placeholder="es. Fluke, Megger, ecc."
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Modello"
                value={formData.modello}
                onChange={(e) => handleInputChange('modello', e.target.value)}
                fullWidth
                required
                placeholder="es. 1587, MIT1025, ecc."
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                label="Numero di Serie"
                value={formData.numero_serie}
                onChange={(e) => handleInputChange('numero_serie', e.target.value)}
                fullWidth
                required
                placeholder="Numero di serie univoco"
              />
            </Grid>

            {/* Informazioni Calibrazione */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ mt: 2 }}>
                Calibrazione
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <DatePicker
                label="Data Calibrazione"
                value={formData.data_calibrazione}
                onChange={(date) => handleDateChange('data_calibrazione', date)}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
                inputFormat="dd/MM/yyyy"
                maxDate={new Date()}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <DatePicker
                label="Data Scadenza Calibrazione"
                value={formData.data_scadenza_calibrazione}
                onChange={(date) => handleDateChange('data_scadenza_calibrazione', date)}
                renderInput={(params) => (
                  <TextField {...params} fullWidth required />
                )}
                inputFormat="dd/MM/yyyy"
                minDate={formData.data_calibrazione}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Percorso Certificato di Calibrazione"
                value={formData.certificato_calibrazione}
                onChange={(e) => handleInputChange('certificato_calibrazione', e.target.value)}
                fullWidth
                placeholder="Percorso del file del certificato (opzionale)"
                helperText="Percorso relativo o assoluto del file del certificato di calibrazione"
              />
            </Grid>

            {/* Note */}
            <Grid item xs={12}>
              <TextField
                label="Note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                fullWidth
                multiline
                rows={3}
                placeholder="Note aggiuntive sullo strumento (opzionale)"
              />
            </Grid>

            {/* Pulsanti */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={onCancel}
                  disabled={loading}
                >
                  Annulla
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={loading}
                >
                  {loading ? 'Salvataggio...' : 'Salva Strumento'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </LocalizationProvider>
  );
}

export default StrumentoForm;
