import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ComposedChart,
  Line,
  LineChart
} from 'recharts';
import { Box, Typography, Grid, Paper, Chip } from '@mui/material';

const COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f',
  purple: '#9c27b0',
  teal: '#00695c'
};

const BoqChart = ({ data }) => {
  if (!data) return null;

  // Prepara dati per grafici cavi per tipologia
  const caviData = data.cavi_per_tipo?.map((cavo, index) => ({
    ...cavo,
    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,
    color: Object.values(COLORS)[index % Object.values(COLORS).length],
    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali || 0)),
    surplus: Math.max(0, (cavo.metri_reali || 0) - cavo.metri_da_posare)
  })) || [];

  // Prepara dati per grafici bobine disponibili
  const bobineData = data.bobine_per_tipo?.map((bobina, index) => ({
    ...bobina,
    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,
    color: Object.values(COLORS)[index % Object.values(COLORS).length]
  })) || [];

  // Calcola totali per grafici a torta
  const totaliCavi = caviData.reduce((acc, cavo) => {
    acc.teorici += cavo.metri_teorici || 0;
    acc.reali += cavo.metri_reali || 0;
    acc.da_posare += cavo.metri_da_posare || 0;
    return acc;
  }, { teorici: 0, reali: 0, da_posare: 0 });

  const totaliData = [
    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },
    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },
    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }
  ];

  // Analisi deficit/surplus
  const analisiData = caviData.map(cavo => ({
    tipologia: cavo.tipologia_short,
    tipologia_full: cavo.tipologia,
    deficit: cavo.deficit,
    surplus: cavo.surplus,
    necessita_acquisto: cavo.deficit > 0
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Analisi Bill of Quantities
      </Typography>

      <Grid container spacing={2}>
        {/* Grafico a torta - Distribuzione Totali */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 220 }}>
            <Typography variant="subtitle1" gutterBottom align="center" sx={{ fontSize: '0.9rem' }}>
              Distribuzione Metri Totali
            </Typography>
            <ResponsiveContainer width="100%" height={160}>
              <PieChart>
                <Pie
                  data={totaliData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={60}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {totaliData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico a barre - Metri per Tipologia Cavo */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 220 }}>
            <Typography variant="subtitle1" gutterBottom align="center" sx={{ fontSize: '0.9rem' }}>
              Metri per Tipologia Cavo
            </Typography>
            <ResponsiveContainer width="100%" height={160}>
              <BarChart data={caviData} margin={{ top: 10, right: 20, left: 10, bottom: 40 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="tipologia_short" angle={-45} textAnchor="end" height={60} fontSize={12} />
                <YAxis fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="metri_reali" fill={COLORS.success} name="Posati" />
                <Bar dataKey="metri_da_posare" fill={COLORS.warning} name="Da Posare" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Statistiche Riassuntive */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Statistiche Riassuntive per Tipologia
            </Typography>
            <Grid container spacing={2}>
              {caviData.map((cavo, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <Box sx={{
                    textAlign: 'center',
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    borderLeft: `4px solid ${cavo.color}`
                  }}>
                    <Typography variant="subtitle2" gutterBottom>
                      {cavo.tipologia}
                    </Typography>
                    <Typography variant="body2">
                      Sezione: <strong>{cavo.sezione}</strong>
                    </Typography>
                    <Typography variant="body2">
                      Cavi: <strong>{cavo.num_cavi}</strong>
                    </Typography>
                    <Typography variant="body2">
                      Teorici: <strong>{cavo.metri_teorici?.toFixed(0)}m</strong>
                    </Typography>
                    <Typography variant="body2">
                      Reali: <strong>{cavo.metri_reali?.toFixed(0)}m</strong>
                    </Typography>
                    <Typography variant="body2">
                      Da Posare: <strong>{cavo.metri_da_posare?.toFixed(0)}m</strong>
                    </Typography>
                    {cavo.deficit > 0 && (
                      <Chip
                        label={`Deficit: ${cavo.deficit.toFixed(0)}m`}
                        color="error"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    )}
                    {cavo.surplus > 0 && (
                      <Chip
                        label={`Surplus: ${cavo.surplus.toFixed(0)}m`}
                        color="success"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Riepilogo Bobine Disponibili */}
        {bobineData.length > 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Riepilogo Bobine Disponibili
              </Typography>
              <Grid container spacing={2}>
                {bobineData.map((bobina, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                    <Box sx={{
                      textAlign: 'center',
                      p: 2,
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      borderLeft: `4px solid ${bobina.color}`
                    }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {bobina.tipologia}
                      </Typography>
                      <Typography variant="body2">
                        Sezione: <strong>{bobina.sezione}</strong>
                      </Typography>
                      <Typography variant="body2">
                        Bobine: <strong>{bobina.num_bobine}</strong>
                      </Typography>
                      <Typography variant="body2">
                        Disponibili: <strong>{bobina.metri_disponibili?.toFixed(0)}m</strong>
                      </Typography>
                      <Chip
                        label="Disponibile"
                        color="success"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default BoqChart;
