import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  CircularProgress,
  FormHelperText,
  IconButton,
  Chip,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Search as SearchIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  AddCircleOutline as AddCircleOutlineIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import caviService from '../../services/caviService';
import axiosInstance from '../../services/axiosConfig';
import IncompatibleReelDialog from './IncompatibleReelDialog';
import CavoDetailsView from './CavoDetailsView';
import {
  CABLE_STATES,
  REEL_STATES,
  determineCableState,
  determineReelState,
  canModifyCable,
  isCableSpare,
  isCableInstalled,
  getCableStateColor,
  getReelStateColor
} from '../../utils/stateUtils';
import parcoCaviService from '../../services/parcoCaviService';
import { redirectToVisualizzaCavi } from '../../utils/navigationUtils';

/**
 * Componente per l'inserimento dei metri posati di un cavo
 * Versione semplificata con workflow compresso in un'unica pagina
 *
 * @param {Object} props - Proprietà del componente
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 */
const InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {
  const navigate = useNavigate();

  // Stati per la gestione del form
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(false);
  const [bobineLoading, setBobineLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Stati per i dati
  const [cavi, setCavi] = useState([]);
  const [bobine, setBobine] = useState([]);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [cavoIdInput, setCavoIdInput] = useState('');

  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)
  const [activeStep, setActiveStep] = useState(0);

  // Stati per il form
  const [formData, setFormData] = useState({
    id_cavo: '',
    metri_posati: '',
    id_bobina: ''
  });

  // Stati per la validazione
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});

  // Stati per i dialoghi speciali
  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);
  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità
  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });
  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);

  // Stati per i filtri delle bobine
  const [searchText, setSearchText] = useState('');
  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);
  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);

  // Gestisce la selezione di una bobina
  const handleSelectBobina = (idBobina) => {
    console.log('Bobina selezionata:', idBobina);
    setFormData({
      ...formData,
      id_bobina: idBobina
    });

    // Reset degli errori
    setFormErrors(prev => ({
      ...prev,
      id_bobina: null
    }));

    // Forza il re-render per mostrare le informazioni della bobina selezionata
    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);
    if (selectedBobina) {
      console.log('Dettagli bobina selezionata:', selectedBobina);
    }
  };


  // Gestisce il cambio del testo di ricerca
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  // Carica la lista delle bobine all'avvio
  useEffect(() => {
    loadBobine();
  }, [cantiereId]);

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina) => {
    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1];
    }
    return idBobina;
  };

  // Funzione per caricare i cavi
  const loadCavi = async () => {
    try {
      setCaviLoading(true);
      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);

      // Carica tutti i cavi, inclusi quelli SPARE e installati
      try {
        const caviData = await caviService.getCavi(cantiereId);
        console.log(`Caricati ${caviData.length} cavi`);

        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)
        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina
        setCavi(caviData);
      } catch (loadError) {
        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);

        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa
        if (loadError.isNetworkError || !loadError.response ||
            loadError.code === 'ECONNABORTED' ||
            (loadError.message && loadError.message.includes('Network Error'))) {

          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');
          await new Promise(resolve => setTimeout(resolve, 1000));

          try {
            // Secondo tentativo con timeout aumentato
            const token = localStorage.getItem('token');
            const API_URL = axiosInstance.defaults.baseURL;

            // Usa l'ID cantiere originale per la richiesta alternativa
            const retryResponse = await axios.get(
              `${API_URL}/cavi/${cantiereId}`,
              {
                headers: {
                  'Authorization': `Bearer ${token}`
                },
                timeout: 30000 // 30 secondi
              }
            );

            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);
            setCavi(retryResponse.data);
          } catch (retryError) {
            console.error('Anche il secondo tentativo è fallito:', retryError);
            throw retryError;
          }
        } else {
          throw loadError;
        }
      }
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);

      // Messaggio di errore più dettagliato
      let errorMessage = 'Errore nel caricamento dei cavi';

      if (error.isNetworkError) {
        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';
      } else if (error.detail) {
        errorMessage = error.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      onError(errorMessage);
    } finally {
      setCaviLoading(false);
    }
  };

  // Funzione per caricare le bobine
  const loadBobine = async () => {
    try {
      setBobineLoading(true);
      console.log('Caricamento bobine iniziato...');

      // Carica tutte le bobine disponibili
      const bobineData = await parcoCaviService.getBobine(cantiereId);
      console.log(`Bobine caricate: ${bobineData.length}`);

      // Filtra solo per stato (disponibile o in uso) e metri residui > 0
      const bobineUtilizzabili = bobineData.filter(bobina =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      );

      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);

      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte
      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {
        console.log('Cavo selezionato, evidenziando bobine compatibili...');
        console.log('Dati cavo:', {
          id_cavo: selectedCavo.id_cavo,
          tipologia: selectedCavo.tipologia,
          sezione: selectedCavo.sezione
        });

        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui
        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();
        const cavoSezione = String(selectedCavo.sezione || '0').trim();

        // Identifica le bobine compatibili
        const bobineCompatibili = [];
        const bobineNonCompatibili = [];

        // Dividi le bobine in compatibili e non compatibili
        bobineUtilizzabili.forEach(bobina => {
          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();
          const bobinaSezione = String(bobina.sezione || '0').trim();

          // Verifica compatibilità
          const tipologiaMatch = bobinaTipologia === cavoTipologia;
          const sezioneMatch = bobinaSezione === cavoSezione;
          const isCompatible = tipologiaMatch && sezioneMatch;

          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {
            'Tipologia bobina': `"${bobina.tipologia}"`,
            'Tipologia cavo': `"${selectedCavo.tipologia}"`,
            'Tipologie uguali?': tipologiaMatch,
            'Sezione bobina': `"${String(bobina.sezione)}"`,
            'Sezione cavo': `"${String(selectedCavo.sezione)}"`,
            'Sezioni uguali?': sezioneMatch,
            'Stato bobina': bobina.stato_bobina,
            'Metri residui': bobina.metri_residui,
            'Compatibile?': isCompatible
          });

          if (isCompatible) {
            bobineCompatibili.push(bobina);
          } else {
            bobineNonCompatibili.push(bobina);
          }
        });

        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);
        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);

        // Log dettagliato delle bobine compatibili
        if (bobineCompatibili.length > 0) {
          console.log('Dettaglio bobine compatibili:');
          bobineCompatibili.forEach(bobina => {
            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);
          });
        } else {
          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');
        }

        // Ordina entrambi gli array per metri residui (decrescente)
        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);
        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);

        // Concatena gli array: prima le compatibili, poi le non compatibili
        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];

        // Imposta le bobine nel componente
        setBobine(bobineOrdinate);
      } else {
        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui
        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');
        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);
        setBobine(bobineUtilizzabili);
      }
    } catch (error) {
      console.error('Errore nel caricamento delle bobine:', error);
      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setBobineLoading(false);
    }
  };

  // Gestisce la ricerca di un cavo per ID o pattern
  const handleSearchCavoById = async () => {
    if (!cavoIdInput.trim()) {
      onError('Inserisci un ID cavo valido');
      return;
    }

    try {
      setCaviLoading(true);

      // Dividi l'input in termini di ricerca separati da virgola
      const searchTerms = cavoIdInput.split(',').map(term => term.trim()).filter(term => term.length > 0);
      console.log(`Ricerca cavi con ${searchTerms.length} termini: ${searchTerms.join(', ')} nel cantiere ${cantiereId}`);

      // Cerca tutti i cavi
      const caviData = await caviService.getCavi(cantiereId);

      // Filtra i cavi che corrispondono ad almeno uno dei termini di ricerca
      const filteredCavi = caviData.filter(cavo => 
        searchTerms.some(term => {
          // Converti il termine di ricerca in stringa per gestire correttamente i numeri
          const termStr = String(term);

          // Verifica se il termine è un numero
          const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

          // Se il termine è un numero, cerca corrispondenze nei campi numerici
          if (isNumericTerm) {
            const numericTerm = parseFloat(termStr);

            // Verifica se il numero è contenuto nell'ID del cavo
            if (cavo.id_cavo.includes(termStr)) {
              return true;
            }

            // Verifica corrispondenza nei metri teorici
            if (cavo.metri_teorici === numericTerm) {
              return true;
            }

            // Verifica corrispondenza nella sezione
            if (String(cavo.sezione || '').includes(termStr)) {
              return true;
            }
          }

          // Altrimenti usa la ricerca standard con includes
          return cavo.id_cavo.toLowerCase().includes(term.toLowerCase());
        })
      );

      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti ai termini di ricerca`);

      // Cerca corrispondenze esatte per qualsiasi termine di ricerca
      const exactMatches = filteredCavi.filter(cavo => 
        searchTerms.some(term => 
          cavo.id_cavo.toLowerCase() === term.toLowerCase()
        )
      );

      if (exactMatches.length === 1) {
        // Se c'è una sola corrispondenza esatta, seleziona direttamente quel cavo
        console.log('Trovata una corrispondenza esatta:', exactMatches[0]);

        // Verifica se il cavo è già installato
        if (exactMatches[0].stato_installazione === 'Installato' || 
            (exactMatches[0].metratura_reale && exactMatches[0].metratura_reale > 0)) {
          console.log('Cavo già installato, mostra dialogo:', exactMatches[0]);
          setAlreadyLaidCavo(exactMatches[0]);
          setShowAlreadyLaidDialog(true);
          setCaviLoading(false);
          return;
        }

        // Verifica se il cavo è SPARE
        if (exactMatches[0].modificato_manualmente === 3) {
          console.log('Cavo SPARE trovato:', exactMatches[0]);
          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect
        }

        // Seleziona il cavo direttamente
        handleCavoSelect(exactMatches[0]);
      } else if (filteredCavi.length > 0) {
        // Mostra i risultati della ricerca in una tabella
        setSearchResults(filteredCavi);
        setShowSearchResults(true);
      } else {
        // Nessun cavo trovato
        onError(`Nessun cavo trovato con i termini "${searchTerms.join(', ')}" nel cantiere ${cantiereId}`);
      }
    } catch (error) {
      console.error('Errore nella ricerca del cavo:', error);

      // Messaggio di errore più dettagliato
      let errorMessage = 'Errore nella ricerca dei cavi';

      if (error.isNetworkError) {
        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';
      } else if (error.status === 404) {
        errorMessage = `Cavo con ID "${cavoIdInput.trim()}" non trovato nel cantiere ${cantiereId}`;
      } else if (error.detail) {
        errorMessage = error.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      onError(errorMessage);
    } finally {
      setCaviLoading(false);
    }
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    // Verifica se il cavo è già installato
    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {
      // Mostra il dialogo per cavi già posati
      setAlreadyLaidCavo(cavo);
      setShowAlreadyLaidDialog(true);
      return;
    }
    // Verifica se il cavo è SPARE (modificato_manualmente = 3)
    else if (cavo.modificato_manualmente === 3) {
      // Chiedi conferma all'utente per riattivare il cavo SPARE
      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {
        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)
        reactivateSpare(cavo.id_cavo).then(() => {
          // Aggiorna il cavo selezionato con modificato_manualmente = 0
          const updatedCavo = { ...cavo, modificato_manualmente: 0 };
          setSelectedCavo(updatedCavo);
          setFormData({
            ...formData,
            id_cavo: updatedCavo.id_cavo,
            metri_posati: ''
          });
          // Nascondi i risultati della ricerca
          setShowSearchResults(false);

          // Carica le bobine compatibili
          loadBobine();
        }).catch(error => {
          console.error('Errore durante la riattivazione del cavo SPARE:', error);
          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));
        });
      } else {
        // L'utente ha annullato la riattivazione
        return;
      }
    } else {
      // Cavo normale (non SPARE e non installato)
      setSelectedCavo(cavo);
      setFormData({
        ...formData,
        id_cavo: cavo.id_cavo,
        metri_posati: ''
      });
      // Nascondi i risultati della ricerca
      setShowSearchResults(false);

      // Carica le bobine compatibili
      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {
        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);
        loadBobine();
      }
    }
  };

  // Funzione per riattivare un cavo SPARE
  const reactivateSpare = async (cavoId) => {
    try {
      // Chiamata API per riattivare il cavo SPARE
      await caviService.reactivateSpare(cantiereId, cavoId);
      onSuccess(`Cavo ${cavoId} riattivato con successo`);
      return true;
    } catch (error) {
      console.error('Errore durante la riattivazione del cavo SPARE:', error);
      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));
      throw error;
    }
  };

  // Gestisce il cambio dei valori nel form
  const handleFormChange = (e) => {
    const { name, value } = e.target;

    // Gestione speciale per il cambio di bobina
    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {
      // Verifica compatibilità tra cavo e bobina
      const bobina = bobine.find(b => b.id_bobina === value);

      if (bobina && selectedCavo) {
        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)
        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined
        const isCompatible =
          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();

        if (!isCompatible) {
          console.log('Bobina incompatibile selezionata:', bobina);
          console.log('Cavo corrente:', selectedCavo);
          console.log('Confronto formazione:', {
            cavoFormazione: String(selectedCavo.sezione || '0'),
            bobinaFormazione: String(bobina.sezione || '0')
          });

          // Mostra il dialogo di incompatibilità
          setIncompatibleReelData({
            cavo: selectedCavo,
            bobina: bobina
          });
          setShowIncompatibleReelDialog(true);

          // Non aggiornare il valore del form finché l'utente non conferma
          return;
        }
      }
    }

    setFormData({
      ...formData,
      [name]: value
    });

    // Validazione in tempo reale
    validateField(name, value);
  };

  // Validazione di un campo
  const validateField = (name, value) => {
    let error = null;
    let warning = null;

    if (name === 'metri_posati') {
      // Controllo input vuoto
      if (!value || value.trim() === '') {
        error = 'Inserire un valore per i metri posati';
        return false;
      }

      // Controllo formato numerico
      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {
        error = 'Inserire un valore numerico positivo';
        return false;
      }

      const metriPosati = parseFloat(value);

      // Controllo metri teorici cavo
      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {
        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;
      }

      // Controllo metri residui bobina (se bobina reale)
      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {
        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);
        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {
          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;
        }
      }
    } else if (name === 'id_bobina') {
      // Controllo che sia selezionata una bobina
      if (!value || value.trim() === '') {
        error = 'È necessario selezionare una bobina';
        return false;
      }
    }

    // Aggiorna gli errori
    setFormErrors(prev => ({
      ...prev,
      [name]: error
    }));

    // Aggiorna gli avvisi
    setFormWarnings(prev => ({
      ...prev,
      [name]: warning
    }));

    return !error;
  };

  // Stati per i dialoghi di conferma
  const [notificationShown, setNotificationShown] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogProps, setConfirmDialogProps] = useState({
    title: '',
    message: '',
    onConfirm: () => {}
  });

  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra

  // Validazione completa del form
  const validateForm = () => {
    let isValid = true;
    const errors = {};
    const warnings = {};

    // Reset della variabile di notifica
    setNotificationShown(false);

    // Validazione metri posati
    if (!formData.metri_posati || formData.metri_posati.trim() === '') {
      errors.metri_posati = 'Inserire un valore per i metri posati';
      isValid = false;
    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {
      errors.metri_posati = 'Inserire un valore numerico positivo';
      isValid = false;
    }

    // Validazione bobina (deve essere selezionata)
    if (!formData.id_bobina || formData.id_bobina.trim() === '') {
      errors.id_bobina = 'È necessario selezionare una bobina';
      isValid = false;
    }

    console.log('Validazione form:', { isValid, errors, warnings, formData });

    if (isValid) {
      const metriPosati = parseFloat(formData.metri_posati);

      // Controllo metri teorici cavo
      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {
        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;
        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate
        // Non mostrare più il popup di conferma, solo l'avviso nel form
        // Continua con la validazione senza interrompere il flusso
      }

      // Controllo metri residui bobina (se bobina reale)
      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {
        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);
        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {
          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;
          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate

          // Mostra il dialogo di conferma invece di window.confirm
          setConfirmDialogProps({
            title: 'Attenzione: Bobina in stato OVER',
            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,
            onConfirm: () => {
              // Continua con la validazione
              handleNext();
            }
          });
          setShowConfirmDialog(true);
          return false; // Interrompi la validazione fino alla conferma dell'utente
        }
      }
    }

    setFormErrors(errors);
    setFormWarnings(warnings);
    return isValid;
  };

  // Gestisce il passaggio al passo successivo
  const handleNext = () => {
    if (activeStep === 2) {
      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)
      if (!validateForm()) {
        return;
      }
    } else if (activeStep === 1) {
      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)
      loadBobine();
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Gestisce il ritorno al passo precedente
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Gestisce il reset del form
  const handleReset = () => {
    setActiveStep(0);
    setSelectedCavo(null);
    setCavoIdInput('');
    setFormData({
      id_cavo: '',
      metri_posati: '',
      id_bobina: ''
    });
    setFormErrors({});
    setFormWarnings({});
  };

  // Determina lo stato di installazione in base ai metri posati
  // Utilizziamo la funzione dalla utility stateUtils
  const determineInstallationStatus = (metriPosati, metriTeorici) => {
    return determineCableState(metriPosati, metriTeorici);
  };

  // Gestisce l'invio del form
  const handleSubmit = async () => {
    // Dichiarazione delle variabili al di fuori del blocco try/catch
    // in modo che siano accessibili anche nel blocco catch
    let idBobina;
    let statoInstallazione;
    let metriPosati;
    let forceOver = false;

    try {
      setLoading(true);

      // Validazione finale
      if (!selectedCavo || !formData.metri_posati || !formData.id_bobina) {
        console.log('Validazione fallita:', {
          selectedCavo: !!selectedCavo,
          metri_posati: !!formData.metri_posati,
          id_bobina: !!formData.id_bobina
        });
        setLoading(false);
        return;
      }

      // Log per debug
      console.log('Validazione passata, procedendo con il salvataggio:', {
        selectedCavo: selectedCavo.id_cavo,
        metri_posati: formData.metri_posati,
        id_bobina: formData.id_bobina
      });

      // Prepara i dati da inviare
      metriPosati = parseFloat(formData.metri_posati);

      // Gestione speciale per BOBINA_VUOTA
      idBobina = formData.id_bobina;

      // Gestione differenziata per cavi non posati e cavi posati
      if (!idBobina || idBobina === '') {
        // È necessario selezionare una bobina, anche BOBINA_VUOTA
        setFormErrors({
          ...formErrors,
          id_bobina: 'È necessario selezionare una bobina'
        });
        setLoading(false);
        return;
      } else if (idBobina === 'BOBINA_VUOTA') {
        // Per cavi posati senza bobina specifica
        console.log('Usando BOBINA_VUOTA per il cavo');
        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null
        idBobina = 'BOBINA_VUOTA';
      } else {
        // Per cavi posati con bobina specifica
        console.log(`Usando bobina reale: ${idBobina}`);
      }

      // Determina lo stato di installazione
      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);

      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER
      forceOver = true;
      console.log('Impostando forceOver a true per garantire il completamento dell\'operazione');

      // Log delle condizioni che richiederebbero forceOver
      if (idBobina === 'BOBINA_VUOTA') {
        console.log('Operazione con BOBINA_VUOTA');
      }
      // Per bobine reali, verifica i metri residui
      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {
        const bobina = bobine.find(b => b.id_bobina === idBobina);
        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {
          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);
        }
      }

      // Verifica anche se i metri posati superano i metri teorici
      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {
        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);
      }

      // Log per debug
      console.log('Invio dati:', {
        cantiereId,
        cavoId: formData.id_cavo,
        metriPosati,
        idBobina,
        forceOver,
        statoInstallazione
      });

      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica
      if (!notificationShown) {
        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;

        // Usa il dialogo di conferma invece di window.confirm
        setConfirmDialogProps({
          title: 'Conferma aggiornamento',
          message: confirmMessage,
          onConfirm: async () => {
            // Esegui la chiamata API qui invece che nel flusso principale
            try {
              setLoading(true);

              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER
              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\'operazione');
              await caviService.updateMetriPosati(
                cantiereId,
                formData.id_cavo,
                metriPosati,
                idBobina,
                true // Forza sempre a true per evitare blocchi
              );

              // Messaggio di successo con dettagli sulla bobina
              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;
              if (idBobina === 'BOBINA_VUOTA') {
                successMessage += '. Cavo associato a BOBINA VUOTA';
              } else if (idBobina) {
                const bobina = bobine.find(b => b.id_bobina === idBobina);
                if (bobina) {
                  successMessage += `. Cavo associato alla bobina ${idBobina}`;
                }
              }

              // Gestione successo
              onSuccess(successMessage);

              // Reset del form
              handleReset();

              // Ricarica i cavi
              loadCavi();
            } catch (error) {
              console.error('Errore durante l\'aggiornamento dei metri posati:', error);

              // Gestione speciale per BOBINA_VUOTA
              if (idBobina === 'BOBINA_VUOTA' && error.success) {
                // Se è un "errore" di successo per BOBINA_VUOTA, trattiamolo come un successo
                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
                onSuccess(successMessage);

                // Reset del form
                handleReset();

                // Ricarica i cavi
                loadCavi();
                return;
              }

              // Gestione dettagliata degli errori
              let errorMessage = 'Errore durante l\'aggiornamento dei metri posati';

              if (error.response) {
                // Il server ha risposto con un codice di errore
                const status = error.response.status;
                const detail = error.response.data?.detail || error.message;

                if (status === 400) {
                  // Errore di validazione
                  if (detail.includes('metri residui')) {
                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione "BOBINA VUOTA" o seleziona un'altra bobina.`;
                  } else if (detail.includes('già posato')) {
                    errorMessage = `Il cavo risulta già posato. Usa la funzione "Modifica bobina cavo posato".`;
                  } else {
                    errorMessage = detail;
                  }
                } else if (status === 404) {
                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata
                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {
                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
                    onSuccess(successMessage);

                    // Reset del form
                    handleReset();

                    // Ricarica i cavi
                    loadCavi();
                    return;
                  } else {
                    errorMessage = `Cavo o bobina non trovati: ${detail}`;
                  }
                } else {
                  errorMessage = `Errore del server (${status}): ${detail}`;
                }
              } else if (error.request) {
                // La richiesta è stata inviata ma non è stata ricevuta risposta
                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';
              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {
                // Gestione speciale per errori con BOBINA_VUOTA
                errorMessage = error.detail;
                if (error.status === 200 || error.success) {
                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
                  onSuccess(successMessage);

                  // Reset del form
                  handleReset();

                  // Ricarica i cavi
                  loadCavi();
                  return;
                }
              } else {
                // Errore durante la configurazione della richiesta
                errorMessage = error.message || error.detail || 'Errore sconosciuto';
              }

              onError(errorMessage);
            } finally {
              setLoading(false);
            }
          }
        });
        setShowConfirmDialog(true);
        setLoading(false);
        return;
      }

      // Chiamata API
      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');
      console.log('- cantiereId:', cantiereId);
      console.log('- id_cavo:', formData.id_cavo);
      console.log('- metri_posati:', metriPosati);
      console.log('- id_bobina:', idBobina, typeof idBobina);
      console.log('- forceOver:', forceOver);

      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER
      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\'operazione');
      await caviService.updateMetriPosati(
        cantiereId,
        formData.id_cavo,
        metriPosati,
        idBobina,
        true // Forza sempre a true per evitare blocchi
      );

      // Messaggio di successo con dettagli sulla bobina
      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;
      if (idBobina === 'BOBINA_VUOTA') {
        successMessage += '. Cavo associato a BOBINA VUOTA';
      } else if (idBobina) {
        const bobina = bobine.find(b => b.id_bobina === idBobina);
        if (bobina) {
          successMessage += `. Cavo associato alla bobina ${idBobina}`;
        }
      }

      // Gestione successo
      onSuccess(successMessage);

      // Reset del form
      handleReset();

      // Ricarica i cavi
      loadCavi();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento dei metri posati:', error);

      // Gestione speciale per BOBINA_VUOTA
      if (idBobina === 'BOBINA_VUOTA' && error.success) {
        // Se è un "errore" di successo per BOBINA_VUOTA, trattiamolo come un successo
        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
        onSuccess(successMessage);

        // Reset del form
        handleReset();

        // Ricarica i cavi
        loadCavi();
        return;
      }

      // Gestione dettagliata degli errori
      let errorMessage = 'Errore durante l\'aggiornamento dei metri posati';

      if (error.response) {
        // Il server ha risposto con un codice di errore
        const status = error.response.status;
        const detail = error.response.data?.detail || error.message;

        if (status === 400) {
          // Errore di validazione
          if (detail.includes('metri residui')) {
            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione "BOBINA VUOTA" o seleziona un'altra bobina.`;
          } else if (detail.includes('già posato')) {
            errorMessage = `Il cavo risulta già posato. Usa la funzione "Modifica bobina cavo posato".`;
          } else {
            errorMessage = detail;
          }
        } else if (status === 404) {
          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata
          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {
            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
            onSuccess(successMessage);

            // Reset del form
            handleReset();

            // Ricarica i cavi
            loadCavi();
            return;
          } else {
            errorMessage = `Cavo o bobina non trovati: ${detail}`;
          }
        } else {
          errorMessage = `Errore del server (${status}): ${detail}`;
        }
      } else if (error.request) {
        // La richiesta è stata inviata ma non è stata ricevuta risposta
        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';
      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {
        // Gestione speciale per errori con BOBINA_VUOTA
        errorMessage = error.detail;
        if (error.status === 200 || error.success) {
          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;
          onSuccess(successMessage);

          // Reset del form
          handleReset();

          // Ricarica i cavi
          loadCavi();
          return;
        }
      } else {
        // Errore durante la configurazione della richiesta
        errorMessage = error.message || error.detail || 'Errore sconosciuto';
      }

      onError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Renderizza il passo 1: Selezione del cavo
  const renderStep1 = () => {
    return (
      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
          Seleziona un cavo
        </Typography>

        {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}
        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Typography variant="subtitle2" sx={{ mr: 1, minWidth: '80px' }}>
              Cerca cavo
            </Typography>
            <TextField
              size="small"
              label="ID Cavo"
              variant="outlined"
              value={cavoIdInput}
              onChange={(e) => setCavoIdInput(e.target.value)}
              placeholder="Inserisci ID cavo (separati da virgola per ricerca multipla)"
              sx={{ flexGrow: 0, width: '250px', mr: 1 }}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearchCavoById}
              disabled={caviLoading || !cavoIdInput.trim()}
              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize="small" />}
              size="small"
              sx={{ minWidth: '80px', height: '36px', mr: 2 }}
            >
              CERCA
            </Button>

            {/* Dettagli cavo e bobina selezionati in riga singola */}
            {selectedCavo && (
              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>
                    Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>
                  </Typography>
                  <Divider orientation="vertical" flexItem sx={{ mx: 1.5 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                      <Chip
                        size="small"
                        label={selectedCavo.stato_installazione || 'N/D'}
                        color={getCableStateColor(selectedCavo.stato_installazione)}
                        sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                      />
                    </Box>
                  </Box>
                </Box>

                {formData.id_bobina && (
                  <>
                    <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>
                        Bobina: <span>{formData.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(formData.id_bobina)}</span>
                      </Typography>
                      {(() => {
                        if (formData.id_bobina === 'BOBINA_VUOTA') {
                          return (
                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                              <Typography variant="body2" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>
                                (Cavo posato senza bobina specifica)
                              </Typography>
                            </Box>
                          );
                        }

                        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);
                        return bobina ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>
                              <Typography variant="body2" sx={{ fontSize: '0.9rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main', fontWeight: 'bold' }}>
                                {bobina.metri_residui || 0} m
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>
                              <Chip
                                size="small"
                                label={bobina.stato_bobina || 'N/D'}
                                color={getReelStateColor(bobina.stato_bobina)}
                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}
                              />
                            </Box>
                          </Box>
                        ) : null;
                      })()}
                    </Box>
                  </>
                )}


              </Box>
            )}
          </Box>
        </Paper>

        {/* Lista cavi - versione compatta */}
        <Paper sx={{ p: 1.5, width: '100%' }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            Seleziona dalla lista
          </Typography>

          {caviLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : cavi.length === 0 ? (
            <Alert severity="info" sx={{ py: 0.5 }}>
              <Typography variant="caption">Non ci sono cavi disponibili da installare.</Typography>
            </Alert>
          ) : (
            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>
                    <TableCell>ID Cavo</TableCell>
                    <TableCell>Tipologia</TableCell>
                    <TableCell>Formazione</TableCell>
                    <TableCell>Metri</TableCell>
                    <TableCell>Stato</TableCell>
                    <TableCell align="center" sx={{ width: '40px' }}>Info</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {cavi.map((cavo) => (
                    <TableRow
                      key={cavo.id_cavo}
                      hover
                      onClick={() => handleCavoSelect(cavo)}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': { bgcolor: '#f1f8e9' },
                        '& td': { py: 0.5 }
                      }}
                    >
                      <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>
                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                      <TableCell>{cavo.sezione || 'N/A'}</TableCell>
                      <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>
                      <TableCell>
                        <Chip
                          size="small"
                          label={isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione}
                          color={isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione)}
                          sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedCavo(cavo);
                            setShowCavoDetailsDialog(true);
                          }}
                        >
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      </Box>
    );
  };

  // Renderizza il passo 2: Inserimento metri
  const renderStep2 = () => {
    if (!selectedCavo) return null;

    return (
      <Box>
        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
          Inserisci metri posati
        </Typography>

        <Paper sx={{ p: 2, width: '100%' }}>
          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
            Inserisci i metri posati
          </Typography>



          <Box sx={{ mt: 2, mb: 2, width: '100%' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
              Metratura posata
            </Typography>
            <TextField
              size="small"
              label="Metri posati"
              variant="outlined"
              name="metri_posati"
              type="number"
              value={formData.metri_posati}
              onChange={handleFormChange}
              error={!!formErrors.metri_posati}
              helperText={formErrors.metri_posati || formWarnings.metri_posati}
              FormHelperTextProps={{
                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }
              }}
              sx={{ mb: 1, width: '200px' }}
              inputProps={{
                max: 999999, // Limite a 6 cifre
                step: 0.1
              }}
            />
          </Box>

          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {formWarnings.metri_posati}
            </Alert>
          )}

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.
            </Typography>
          </Alert>
        </Paper>
      </Box>
    );
  };

  // Renderizza il passo 3: Associazione bobina
  const renderStep3 = () => {

    // Funzione per costruire l'ID completo della bobina
    const buildFullBobinaId = (numeroBobina) => {
      return `C${cantiereId}_B${numeroBobina}`;
    };

    // Verifica se una bobina ha metri residui sufficienti
    const hasSufficientMeters = (bobina) => {
      if (!bobina || !formData.metri_posati) return true;
      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);
    };

    // Filtra le bobine in base al testo di ricerca
    const bobineFiltrate = bobine.filter(bobina => {
      // Se non c'è testo di ricerca, mostra tutte le bobine
      if (!searchText) return true;

      // Dividi il testo di ricerca in termini separati da virgola
      const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);

      // Se ci sono due termini identici (es. "a,a"), cerca una corrispondenza esatta
      if (searchTerms.length === 2 && searchTerms[0] === searchTerms[1]) {
        const exactMatch = 
          getBobinaNumber(bobina.id_bobina).toLowerCase() === searchTerms[0] ||
          String(bobina.tipologia || '').toLowerCase() === searchTerms[0] ||
          String(bobina.sezione || '').toLowerCase() === searchTerms[0];

        return exactMatch;
      }

      // Altrimenti, cerca corrispondenze parziali per ciascun termine
      return searchTerms.some(term => {
        // Converti il termine di ricerca in stringa per gestire correttamente i numeri
        const termStr = String(term);

        // Verifica se il termine è un numero
        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

        // Se il termine è un numero, cerca corrispondenze esatte nei campi numerici
        if (isNumericTerm) {
          const numericTerm = parseFloat(termStr);
          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));

          // Verifica corrispondenza esatta per numeri
          if (bobinaSezioneNum === numericTerm) {
            return true;
          }

          // Verifica anche se il numero è contenuto nell'ID della bobina
          if (getBobinaNumber(bobina.id_bobina).includes(termStr)) {
            return true;
          }
        }

        // Altrimenti usa la ricerca standard con includes
        return getBobinaNumber(bobina.id_bobina).toLowerCase().includes(term) ||
               String(bobina.tipologia || '').toLowerCase().includes(term) ||
               String(bobina.sezione || '').toLowerCase().includes(term);
      });
    });

    // Separa le bobine compatibili e non compatibili
    const bobineCompatibili = selectedCavo
      ? bobineFiltrate.filter(bobina =>
          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&
          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim())
      : bobineFiltrate;

    const bobineNonCompatibili = selectedCavo
      ? bobineFiltrate.filter(bobina =>
          String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() ||
          String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim())
      : [];



    return (
      <Box>
        <Box sx={{ mb: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', display: 'inline', fontSize: '1.1rem' }}>
            Associa bobina
          </Typography>
          <Typography variant="body2" sx={{ display: 'inline', ml: 1, color: 'text.secondary' }}>
            (Seleziona una bobina da associare al cavo o usa "BOBINA VUOTA" se non desideri associare una bobina specifica)
          </Typography>
        </Box>

        <Paper sx={{ p: 2, width: '100%' }}>
          {/* Campo per l'inserimento dei metri posati */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TextField
                size="small"
                label="Metri posati"
                variant="outlined"
                name="metri_posati"
                type="number"
                value={formData.metri_posati}
                onChange={handleFormChange}
                error={!!formErrors.metri_posati}
                helperText={formErrors.metri_posati}
                sx={{ width: '200px' }}
                inputProps={{
                  max: 999999, // Limite a 6 cifre
                  step: 0.1
                }}
              />
              {/* Warning accanto al campo */}
              {formWarnings.metri_posati && !formErrors.metri_posati && (
                <Typography
                  variant="body2"
                  sx={{
                    color: 'warning.main',
                    fontWeight: 'medium',
                    fontSize: '0.875rem'
                  }}
                >
                  {formWarnings.metri_posati}
                </Typography>
              )}
            </Box>
          </Box>



          <Divider sx={{ my: 2 }} />

          <Box sx={{ mb: 2 }}>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              {/* Campo di ricerca - versione compatta */}
              <TextField
                size="small"
                label="Cerca"
                variant="outlined"
                value={searchText}
                onChange={handleSearchTextChange}
                placeholder="ID, tipologia... (usa a,a per selezionare esattamente 'a')"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                  endAdornment: searchText ? (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        aria-label="clear search"
                        onClick={() => setSearchText('')}
                        edge="end"
                      >
                        <CancelIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ) : null
                }}
                sx={{ width: '200px' }}
              />

              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  console.log('Selezionata BOBINA_VUOTA');
                  handleSelectBobina('BOBINA_VUOTA');
                }}
                sx={{ height: '40px', fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',
                      bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                      border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined }}
              >
                BOBINA VUOTA
              </Button>
            </Box>
          </Box>

          {bobineLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>
              <CircularProgress size={24} />
            </Box>
          ) : (
            <Box>


              {/* Griglia per le due liste di bobine */}
              <Grid container spacing={2}>
                {/* Colonna sinistra: Bobine compatibili */}
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                      ELENCO BOBINE COMPATIBILI
                    </Typography>

                    {bobineCompatibili.length > 0 ? (
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                          <Box sx={{ width: '60px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                          </Box>
                          <Box sx={{ width: '120px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                          </Box>
                          <Box sx={{ width: '100px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                          </Box>
                          <Box sx={{ width: '100px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                          </Box>
                          <Box sx={{ flexGrow: 0 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                          </Box>
                        </Box>
                      <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                        {bobineCompatibili.map((bobina) => (
                          <ListItem
                            key={bobina.id_bobina}
                            disablePadding
                            secondaryAction={
                              <IconButton
                                edge="end"
                                size="small"
                                onClick={() => handleSelectBobina(bobina.id_bobina)}
                              >
                                <AddCircleOutlineIcon color="primary" />
                              </IconButton>
                            }
                            sx={{
                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                              borderRadius: '4px',
                              mb: 0.5,
                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                            }}
                          >
                            <ListItemButton
                              dense
                              onClick={() => handleSelectBobina(bobina.id_bobina)}
                            >
                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                                <Box sx={{ width: '60px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                    {getBobinaNumber(bobina.id_bobina)}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '120px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {bobina.tipologia || 'N/A'}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '100px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {bobina.sezione || 'N/A'}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '100px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>
                                    {bobina.metri_residui || 0} m
                                  </Typography>
                                </Box>
                                <Box sx={{ flexGrow: 0 }}>
                                  <Chip
                                    size="small"
                                    label={bobina.stato_bobina || 'N/D'}
                                    color={getReelStateColor(bobina.stato_bobina)}
                                    variant="outlined"
                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                  />
                                </Box>
                              </Box>
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                      </>
                    ) : (
                      <Alert severity="info" sx={{ mt: 1 }}>
                        Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.
                      </Alert>
                    )}
                  </Paper>
                </Grid>

                {/* Colonna destra: Bobine non compatibili */}
                <Grid item xs={12} md={6}>
                  <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                      ELENCO BOBINE NON COMPATIBILI
                    </Typography>

                    {bobineNonCompatibili.length > 0 ? (
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>
                          <Box sx={{ width: '60px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>
                          </Box>
                          <Box sx={{ width: '120px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>
                          </Box>
                          <Box sx={{ width: '100px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>
                          </Box>
                          <Box sx={{ width: '100px', mr: 2 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>
                          </Box>
                          <Box sx={{ flexGrow: 0 }}>
                            <Typography variant="caption" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>
                          </Box>
                        </Box>
                      <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>
                        {bobineNonCompatibili.map((bobina) => (
                          <ListItem
                            key={bobina.id_bobina}
                            disablePadding
                            secondaryAction={
                              <IconButton
                                edge="end"
                                size="small"
                                onClick={() => handleSelectBobina(bobina.id_bobina)}
                              >
                                <AddCircleOutlineIcon color="primary" />
                              </IconButton>
                            }
                            sx={{
                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',
                              borderRadius: '4px',
                              mb: 0.5,
                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',
                            }}
                          >
                            <ListItemButton
                              dense
                              onClick={() => handleSelectBobina(bobina.id_bobina)}
                            >
                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>
                                <Box sx={{ width: '60px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                                    {getBobinaNumber(bobina.id_bobina)}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '120px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {bobina.tipologia || 'N/A'}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '100px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                    {bobina.sezione || 'N/A'}
                                  </Typography>
                                </Box>
                                <Box sx={{ width: '100px', mr: 2 }}>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>
                                    {bobina.metri_residui || 0} m
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', gap: 1 }}>
                                  <Chip
                                    size="small"
                                    label={bobina.stato_bobina || 'N/D'}
                                    color={getReelStateColor(bobina.stato_bobina)}
                                    variant="outlined"
                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                  />
                                  <Chip
                                    size="small"
                                    label="Non comp."
                                    color="warning"
                                    variant="outlined"
                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}
                                  />
                                </Box>
                              </Box>
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                      </>
                    ) : (
                      <Alert severity="info" sx={{ mt: 1 }}>
                        Nessuna bobina non compatibile disponibile con i filtri attuali.
                      </Alert>
                    )}
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}



          {bobine.length === 0 && !bobineLoading && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.
            </Alert>
          )}

          {/* Pulsanti Salva e Annulla */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              disabled={loading}
            >
              Annulla
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              disabled={loading || !selectedCavo || !formData.metri_posati || !formData.id_bobina}
              startIcon={loading ? <CircularProgress size={20} /> : null}
            >
              Salva
            </Button>
          </Box>
        </Paper>
      </Box>
    );
  };

  // Renderizza il passo 4: Conferma
  const renderStep4 = () => {

    // Ottieni il numero della bobina se presente
    let numeroBobina = 'Nessuna';
    let bobinaInfo = null;

    if (formData.id_bobina === 'BOBINA_VUOTA') {
      numeroBobina = 'BOBINA VUOTA';
    } else if (formData.id_bobina) {
      numeroBobina = getBobinaNumber(formData.id_bobina);
      // Trova i dettagli della bobina selezionata
      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);
    }

    // Determina lo stato di installazione
    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Conferma inserimento
        </Typography>

        <Paper sx={{ p: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Riepilogo dati
          </Typography>

          {/* Dettagli del cavo */}
          <CavoDetailsView
            cavo={selectedCavo}
            compact={true}
            title="Dettagli del cavo"
          />

          {/* Informazioni sull'operazione */}
          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
              Informazioni sull'operazione:
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Metri Posati:</strong> {formData.metri_posati} m
                </Typography>
                <Typography variant="body2">
                  <strong>Stato Installazione:</strong> {statoInstallazione}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Bobina Associata:</strong> {numeroBobina}
                </Typography>
                {bobinaInfo && (
                  <Typography variant="body2">
                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>

          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (
            <Alert severity="warning" sx={{ mt: 3 }}>
              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).
              Questo porterà la bobina in stato OVER.
            </Alert>
          )}

          <Alert severity="info" sx={{ mt: 3 }}>
            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.
            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}
          </Alert>
        </Paper>
      </Box>
    );
  };

  // Renderizza il contenuto in base al passo attivo
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderStep1(); // Seleziona Cavo
      case 1:
        return renderStep3(); // Associa Bobina
      case 2:
        return renderStep2(); // Inserisci Metri
      case 3:
        return renderStep4(); // Conferma
      default:
        return 'Passo sconosciuto';
    }
  };

  // Gestisce la chiusura del dialogo per cavi già posati
  const handleCloseAlreadyLaidDialog = () => {
    setShowAlreadyLaidDialog(false);
    setAlreadyLaidCavo(null);
  };



  // Gestisce l'opzione di modificare la bobina di un cavo già posato
  const handleModifyReel = () => {
    if (alreadyLaidCavo) {
      navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina/${alreadyLaidCavo.id_cavo}`);
    }
    handleCloseAlreadyLaidDialog();
  };

  // Gestisce l'opzione di selezionare un altro cavo
  const handleSelectAnotherCable = () => {
    handleCloseAlreadyLaidDialog();
    // Reset del form per selezionare un nuovo cavo
    setSelectedCavo(null);
    setCavoIdInput('');
    setShowSearchResults(false);
  };

  // Gestisce la chiusura del dialogo per bobine incompatibili
  const handleCloseIncompatibleReelDialog = () => {
    setShowIncompatibleReelDialog(false);
    setIncompatibleReelData({ cavo: null, bobina: null });
  };

  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina
  const handleUpdateCavoToMatchReel = async () => {
    const { cavo, bobina } = incompatibleReelData;
    if (!cavo || !bobina) {
      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });
      onError('Dati mancanti per aggiornare il cavo');
      return;
    }

    try {
      setLoading(true);
      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);
      console.log('Dati cavo prima dell\'aggiornamento:', cavo);
      console.log('Dati bobina:', bobina);

      // Aggiorna le caratteristiche del cavo
      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);

      // Aggiorna il cavo selezionato con le nuove caratteristiche
      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);
      console.log('Dati cavo dopo l\'aggiornamento:', updatedCavo);
      setSelectedCavo(updatedCavo);

      // Imposta la bobina selezionata
      setFormData({
        ...formData,
        id_bobina: bobina.id_bobina
      });

      // Ricarica le bobine per aggiornare l'interfaccia
      await loadBobine();

      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);
      handleCloseIncompatibleReelDialog();
    } catch (error) {
      console.error('Errore durante l\'aggiornamento delle caratteristiche del cavo:', error);
      onError('Errore durante l\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo
  const handleContinueWithIncompatible = async () => {
    const { cavo, bobina } = incompatibleReelData;
    if (!cavo || !bobina) {
      console.error('Dati mancanti per utilizzare la bobina incompatibile:', { cavo, bobina });
      onError('Dati mancanti per utilizzare la bobina incompatibile');
      return;
    }

    try {
      setLoading(true);
      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);

      // Imposta la bobina selezionata senza aggiornare le caratteristiche del cavo
      setFormData({
        ...formData,
        id_bobina: bobina.id_bobina
      });

      onSuccess(`Bobina incompatibile ${bobina.id_bobina} selezionata per il cavo ${cavo.id_cavo}`);
      handleCloseIncompatibleReelDialog();
    } catch (error) {
      console.error('Errore durante la selezione della bobina incompatibile:', error);
      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  // Gestisce la selezione di un'altra bobina
  const handleSelectAnotherReel = () => {
    handleCloseIncompatibleReelDialog();
    // Reset della bobina selezionata
    setFormData({
      ...formData,
      id_bobina: ''
    });
  };

  return (
    <Box>
      {/* Sezione di ricerca con dettagli cavo selezionato in riga singola */}
      <Box sx={{ mb: 2 }}>
        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Typography variant="subtitle2" sx={{ mr: 1, minWidth: '80px' }}>
              Cerca cavo
            </Typography>
            <TextField
              size="small"
              label="ID Cavo"
              variant="outlined"
              value={cavoIdInput}
              onChange={(e) => setCavoIdInput(e.target.value)}
              placeholder="Inserisci l'ID del cavo"
              sx={{ flexGrow: 0, width: '200px', mr: 1 }}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleSearchCavoById}
              disabled={caviLoading || !cavoIdInput.trim()}
              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize="small" />}
              size="small"
              sx={{ minWidth: '80px', height: '36px', mr: 2 }}
            >
              CERCA
            </Button>

            {/* Dettagli cavo selezionato in riga singola */}
            {selectedCavo && (
              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.9rem' }}>
                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>
                </Typography>
                <Divider orientation="vertical" flexItem sx={{ mx: 1.5 }} />
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>
                    <Chip
                      size="small"
                      label={selectedCavo.stato_installazione || 'N/D'}
                      color={getCableStateColor(selectedCavo.stato_installazione)}
                      sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}
                    />
                  </Box>
                </Box>
              </Box>
            )}
          </Box>
        </Paper>

        <Divider sx={{ my: 2 }} />
      </Box>

      {/* Risultati della ricerca */}
      {showSearchResults && searchResults.length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Risultati della ricerca
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                  <TableCell>ID Cavo</TableCell>
                  <TableCell>Tipologia</TableCell>
                  <TableCell>Formazione</TableCell>
                  <TableCell>Ubicazione</TableCell>
                  <TableCell>Metri Teorici</TableCell>
                  <TableCell>Stato</TableCell>
                  <TableCell>Azioni</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {searchResults.map((cavo) => (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>{cavo.id_cavo}</TableCell>
                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>
                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>
                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>
                    <TableCell>
                      <Chip
                        label={cavo.stato_installazione || 'N/D'}
                        size="small"
                        color={getCableStateColor(cavo.stato_installazione)}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        variant="contained"
                        color="primary"
                        onClick={() => handleCavoSelect(cavo)}
                        disabled={isCableInstalled(cavo)}
                      >
                        Seleziona
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}
      {/* Sezione di selezione bobina */}
      {selectedCavo && (
        <Box>
          {renderStep3()}
        </Box>
      )}

      {/* Dialogo di conferma generico */}
      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'warning.light' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            <Typography variant="h6">{confirmDialogProps.title}</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mt: 2 }}>
            {confirmDialogProps.message}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmDialog(false)} color="secondary" variant="outlined">
            Annulla
          </Button>
          <Button
            onClick={() => {
              setShowConfirmDialog(false);
              confirmDialogProps.onConfirm();
            }}
            color="primary"
            variant="contained"
            autoFocus
          >
            Conferma
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialogo per cavi già posati */}
      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ bgcolor: 'warning.light' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            <Typography variant="h6">Cavo già posato</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {alreadyLaidCavo && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" paragraph>
                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).
              </Typography>
              <Typography variant="body1" paragraph>
                Puoi scegliere di:
              </Typography>
              <Typography variant="body2" component="ul">
                <li>Modificare la bobina associata al cavo</li>
                <li>Selezionare un altro cavo</li>
                <li>Annullare l'operazione</li>
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Button onClick={handleCloseAlreadyLaidDialog} color="secondary">
            Annulla operazione
          </Button>
          <Box>
            <Button onClick={handleSelectAnotherCable} color="primary" sx={{ mr: 1 }}>
              Seleziona altro cavo
            </Button>
            <Button onClick={handleModifyReel} variant="contained" color="primary">
              Modifica bobina
            </Button>
          </Box>
        </DialogActions>
      </Dialog>

      {/* Dialogo per bobine incompatibili */}
      <IncompatibleReelDialog
        open={showIncompatibleReelDialog}
        onClose={handleCloseIncompatibleReelDialog}
        cavo={incompatibleReelData.cavo}
        bobina={incompatibleReelData.bobina}
        onUpdateCavo={handleUpdateCavoToMatchReel}
        onSelectAnotherReel={handleSelectAnotherReel}
        onContinueWithIncompatible={handleContinueWithIncompatible}
      />

      {/* Dialogo per visualizzare i dettagli del cavo */}
      <Dialog
        open={showCavoDetailsDialog}
        onClose={() => setShowCavoDetailsDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon color="primary" />
            <Typography variant="h6">Dettagli Cavo</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <CavoDetailsView cavo={selectedCavo} />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCavoDetailsDialog(false)} color="primary">
            Chiudi
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InserisciMetriForm;
