import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

// Assicurati che axios sia disponibile globalmente per i tentativi alternativi
const API_URL = config.API_URL;

const caviService = {
  // Ottiene la lista dei cavi di un cantiere
  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {
    try {
      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });
      console.log('Tipo di cantiereId:', typeof cantiereId);

      // Verifica che cantiereId sia definito
      if (cantiereId === undefined || cantiereId === null) {
        console.error('cantiereId è undefined o null');
        throw new Error('ID cantiere mancante');
      }

      // Assicurati che cantiereId sia un numero
      let cantiereIdNum = cantiereId;
      if (typeof cantiereId === 'string') {
        cantiereIdNum = parseInt(cantiereId, 10);
        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);
      }

      if (isNaN(cantiereIdNum)) {
        console.error('ID cantiere non è un numero valido:', cantiereId);
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Log dettagliati per debug
      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);

      // Soluzione alternativa per i cavi SPARE
      if (tipoCavo === 3) {
        console.log('Caricamento cavi SPARE con query diretta...');
        try {
          // Usa una query SQL diretta per ottenere i cavi SPARE
          const response = await axios.get(
            `${API_URL}/cavi/spare/${cantiereIdNum}`,
            {
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              timeout: 30000
            }
          );

          console.log('Risposta cavi SPARE:', response.data);
          return response.data;
        } catch (spareError) {
          console.error('Errore nel caricamento dei cavi SPARE:', spareError);
          // Se fallisce, continua con il metodo standard
        }
      }

      // Costruisci l'URL con i parametri di query
      let url = `/cavi/${cantiereIdNum}`;
      const queryParams = [];

      if (tipoCavo !== null) {
        queryParams.push(`tipo_cavo=${tipoCavo}`);
      }

      // Aggiungi filtri aggiuntivi se presenti
      if (filters.stato_installazione) {
        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);
      }

      if (filters.tipologia) {
        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);
      }

      if (filters.sort_by) {
        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);
        if (filters.sort_order) {
          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);
        }
      }

      // Aggiungi i parametri di query all'URL
      if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`;
      }

      // Log dettagliato dell'URL e dei parametri
      console.log('URL API completo:', url);
      console.log('Parametri di query:', queryParams);

      console.log(`Chiamata API: GET ${url}`);
      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');
      console.log('URL completo:', `${API_URL}${url}`);

      try {
        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);
        console.log('Headers della richiesta:', {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        });

        // Aggiungi un timeout più lungo per la richiesta
        const response = await axiosInstance.get(url, { timeout: 60000 });

        console.log(`Risposta API: ${url}`, response.data);
        console.log('Status della risposta:', response.status);
        console.log('Headers della risposta:', response.headers);

        if (Array.isArray(response.data)) {
          console.log(`Numero di cavi ricevuti: ${response.data.length}`);
          if (response.data.length > 0) {
            console.log('Primo cavo ricevuto:', response.data[0]);
          } else {
            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);
          }
        } else {
          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);
        }

        return response.data;
      } catch (apiError) {
        console.error(`Errore nella chiamata API GET ${url}:`, apiError);
        console.error('Dettagli errore API:', {
          message: apiError.message,
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          headers: apiError.response?.headers,
          code: apiError.code,
          isAxiosError: apiError.isAxiosError,
          config: apiError.config ? {
            url: apiError.config.url,
            method: apiError.config.method,
            timeout: apiError.config.timeout,
            headers: apiError.config.headers
          } : 'No config'
        });

        // Gestione specifica per errori di rete
        if (apiError.code === 'ERR_NETWORK') {
          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');
          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile
          try {
            console.log('Tentativo di test di connessione al backend...');
            const testResponse = await fetch(API_URL);
            console.log('Test di connessione al backend:', testResponse.status);
          } catch (testError) {
            console.error('Test di connessione al backend fallito:', testError);
          }
        }

        throw apiError;
      }
    } catch (error) {
      console.error('Get cavi error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,
        stack: error.stack
      });

      // Verifica se l'errore è dovuto a un problema di connessione
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {
        console.error('Errore di connessione o timeout');
        // Ritorna un array vuoto invece di lanciare un errore
        console.log('Ritorno array vuoto come fallback');
        return [];
      }

      // Crea un errore più informativo
      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');
      enhancedError.status = error.response?.status;
      enhancedError.data = error.response?.data;
      enhancedError.response = error.response;
      enhancedError.originalError = error;
      enhancedError.code = error.code;
      enhancedError.isAxiosError = error.isAxiosError;

      throw enhancedError;
    }
  },

  // Crea un nuovo cavo
  createCavo: async (cantiereId, cavoData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);
      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));

      // Invia la richiesta al server
      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta del server:', response.status, response.statusText);
      console.log('Dati ricevuti:', response.data);
      return response.data;
    } catch (error) {
      console.error('Create cavo error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se il cavo è stato creato...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste nel database
          const token = localStorage.getItem('token');
          // Assicurati che cantiereId sia un numero
          const cantiereIdNumber = parseInt(cantiereId, 10);
          const checkResponse = await axios.get(
            `${API_URL}/cavi/${cantiereIdNumber}/check/${cavoData.id_cavo}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`
              },
              timeout: 5000
            }
          );

          if (checkResponse.data && checkResponse.data.exists) {
            console.log('Il cavo risulta creato nonostante l\'errore di comunicazione');
            // Recupera i dati del cavo
            const cavoResponse = await axios.get(
              `${API_URL}/cavi/${cantiereIdNumber}/${cavoData.id_cavo}`,
              {
                headers: {
                  'Authorization': `Bearer ${token}`
                },
                timeout: 5000
              }
            );

            if (cavoResponse.data) {
              console.log('Dati del cavo recuperati:', cavoResponse.data);
              return cavoResponse.data;
            }
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica post-errore:', verifyError);
        }

        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non esiste
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw {
          detail: 'La richiesta non è andata a buon fine, ma il cavo potrebbe essere stato creato. Verifica nella lista dei cavi.',
          status: 0,
          isNetworkError: true
        };
      }

      if (error.response) {
        console.error('Dettagli errore:', error.response.data);
        console.error('Status errore:', error.response.status);
        console.error('Headers errore:', error.response.headers);

        // Formatta il messaggio di errore in modo più leggibile
        const errorDetail = error.response.data.detail || 'Errore sconosciuto';
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Ottiene un cavo specifico per ID
  getCavoById: async (cantiereId, cavoId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Tentativo di ottenere cavo con ID ${cavoId} dal cantiere ${cantiereIdNum}`);

      // Aumenta il timeout per questa richiesta specifica
      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`, {
        timeout: 30000 // 30 secondi
      });

      console.log(`Cavo trovato:`, response.data);
      return response.data;
    } catch (error) {
      console.error('Get cavo by ID error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, tentativo di recupero alternativo...');

        try {
          // Tentativo alternativo con axios standard (non l'istanza configurata)
          console.log(`Tentativo alternativo di recupero del cavo ${cavoId}...`);
          const token = localStorage.getItem('token');
          const API_URL = axiosInstance.defaults.baseURL;

          // Usa l'ID cantiere originale per la richiesta alternativa
          const altResponse = await axios.get(
            `${API_URL}/cavi/${cantiereId}/${cavoId}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`
              },
              timeout: 10000 // 10 secondi
            }
          );

          console.log('Recupero alternativo riuscito:', altResponse.data);
          return altResponse.data;
        } catch (altError) {
          console.error('Anche il tentativo alternativo è fallito:', altError);
          throw {
            detail: 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',
            status: 0,
            isNetworkError: true,
            originalError: error.message
          };
        }
      }

      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna un cavo esistente
  updateCavo: async (cantiereId, cavoId, cavoData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);
      console.log('Dati inviati:', cavoData);

      // Verifica che il backend sia raggiungibile
      try {
        console.log('Verifica connessione al backend...');
        const pingResponse = await fetch(`${API_URL}/health`, { method: 'GET' });
        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);
        if (!pingResponse.ok) {
          console.error('Il server non risponde correttamente:', pingResponse.status);
          throw new Error('Il server non risponde correttamente. Riprova più tardi.');
        }
      } catch (pingError) {
        console.error('Errore durante il ping al backend:', pingError);
        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');
      }

      // Imposta un timeout più lungo per la richiesta
      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {
        timeout: 90000, // 90 secondi (timeout esteso)
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update cavo error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error')) ||
          error.request) {

        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se è stato aggiornato
          const token = localStorage.getItem('token');
          // Assicurati che cantiereId sia un numero
          const cantiereIdNumber = parseInt(cantiereId, 10);
          const cavoResponse = await axios.get(
            `${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`
              },
              timeout: 5000
            }
          );

          if (cavoResponse.data) {
            console.log('Cavo trovato nel database, verifica se è stato aggiornato');

            // Verifica se almeno uno dei campi è stato aggiornato
            let isUpdated = false;
            for (const key in cavoData) {
              if (cavoData[key] !== undefined &&
                  JSON.stringify(cavoData[key]) === JSON.stringify(cavoResponse.data[key])) {
                console.log(`Campo ${key} risulta aggiornato: ${cavoData[key]}`);
                isUpdated = true;
                break;
              }
            }

            if (isUpdated) {
              console.log('Il cavo risulta aggiornato nonostante l\'errore di comunicazione');
              return cavoResponse.data;
            } else {
              console.log('Il cavo esiste ma non risulta aggiornato');
            }
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica post-errore:', verifyError);
        }

        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non è stato aggiornato
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw {
          detail: 'La modifica potrebbe essere stata salvata nonostante l\'errore di comunicazione. Controlla lo stato del cavo.',
          status: 0,
          isNetworkError: true
        };
      }

      // Gestione più dettagliata dell'errore
      if (error.response) {
        // Il server ha risposto con un codice di stato diverso da 2xx
        console.error('Errore dal server:', error.response.status, error.response.statusText);
        console.error('Dati errore:', error.response.data);
        throw error.response.data;
      } else {
        // Si è verificato un errore durante l'impostazione della richiesta
        console.error('Errore durante l\'impostazione della richiesta:', error.message);
        throw { detail: error.message, status: 500 };
      }
    }
  },

  // Ottiene la revisione corrente del cantiere
  getRevisioneCorrente: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);
      return response.data.revisione_corrente;
    } catch (error) {
      console.error('Get revisione corrente error:', error);
      return '00'; // Valore di default in caso di errore
    }
  },

  // Marca un cavo come SPARE
  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Log dettagliati per debug
      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });

      // Prova prima con l'endpoint POST specifico
      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);

      try {
        const postResponse = await axios.post(
          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`,
          { force: force },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000
          }
        );

        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);

        // Verifica che il cavo sia stato effettivamente marcato come SPARE
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verifica lo stato del cavo
        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');
        const cavoResponse = await axios.get(
          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000
          }
        );

        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);

        // Verifica che modificato_manualmente sia 3
        if (cavoResponse.data.modificato_manualmente !== 3) {
          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');
          throw new Error('Il cavo non risulta marcato come SPARE');
        }

        return cavoResponse.data;
      } catch (postError) {
        // Se fallisce il POST, prova con DELETE mode=spare
        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);
        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);

        const deleteResponse = await axios.delete(
          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000,
            params: { mode: 'spare' }
          }
        );

        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);

        // Verifica che il cavo sia stato effettivamente marcato come SPARE
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verifica lo stato del cavo
        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');
        const cavoResponse = await axios.get(
          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000
          }
        );

        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);

        // Verifica che modificato_manualmente sia 3
        if (cavoResponse.data.modificato_manualmente !== 3) {
          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');
          throw new Error('Il cavo non risulta marcato come SPARE');
        }

        return cavoResponse.data;
      }
    } catch (error) {
      console.error('Mark cavo as SPARE error:', error);
      console.error('Dettagli errore:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,
        config: error.config
      });
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina un cavo o lo marca come SPARE
  deleteCavo: async (cantiereId, cavoId, mode = null) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Log dettagliati per debug
      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });

      // Se è specificata la modalità, aggiungi il parametro alla richiesta
      const requestConfig = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        timeout: 30000, // Timeout aumentato a 30 secondi
        params: mode ? { mode } : {}
      };

      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);
      console.log('Config:', requestConfig);

      // Usa axios direttamente invece di axiosInstance per avere più controllo
      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);
      console.log('Risposta deleteCavo:', response.data);
      return response.data;
    } catch (error) {
      console.error('Delete cavo error:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,
        config: error.config
      });

      // Crea un errore più informativo
      if (error.response && error.response.data) {
        throw error.response.data;
      } else if (error.message) {
        throw new Error(error.message);
      } else {
        throw new Error('Errore durante l\'eliminazione del cavo');
      }
    }
  },

  // Aggiorna le caratteristiche di un cavo per renderlo compatibile con una bobina
  updateCavoForCompatibility: async (cantiereId, cavoId, idBobina) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);
      console.log('Dati:', { id_bobina: idBobina });

      // Prepara i dati da inviare
      const requestData = {
        id_bobina: idBobina
      };

      // Imposta un timeout più lungo per questa operazione
      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, requestData, {
        timeout: 30000, // 30 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update cavo for compatibility error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      if (error.response && error.response.data) {
        let errorDetail = error.response.data.detail || 'Errore sconosciuto';
        // Se errorDetail è un oggetto, convertilo in stringa
        if (typeof errorDetail === 'object') {
          errorDetail = JSON.stringify(errorDetail);
        }
        throw { detail: errorDetail, status: error.response.status };
      }
      // Se è un errore di validazione locale, formatta il messaggio
      if (error instanceof Error) {
        throw { detail: error.message, status: 400 };
      }
      throw error;
    }
  },

  // Aggiorna i metri posati di un cavo
  updateMetriPosati: async (cantiereId, cavoId, metriPosati, idBobina = null, forceOver = false) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);
      console.log('Dati:', { metri_posati: metriPosati, id_bobina: idBobina, force_over: forceOver });

      // Prepara i dati da inviare
      const requestData = {
        metri_posati: metriPosati,
        data_posa: new Date().toISOString() // Data corrente
      };

      // Gestione speciale per BOBINA_VUOTA e altri valori di id_bobina
      if (idBobina === 'BOBINA_VUOTA') {
        // Assicurati che BOBINA_VUOTA venga inviato come stringa
        requestData.id_bobina = 'BOBINA_VUOTA';
        console.log('Impostando id_bobina a BOBINA_VUOTA (stringa)');

        // Quando si usa BOBINA_VUOTA, imposta sempre force_over a true
        // per evitare problemi con la validazione
        requestData.force_over = true;
        console.log('Impostando force_over a true per BOBINA_VUOTA');
      } else if (idBobina !== null && idBobina !== undefined) {
        // Per altri valori di bobina
        requestData.id_bobina = idBobina;
        console.log(`Impostando id_bobina a ${idBobina}`);
      } else {
        // Se non è specificata una bobina, imposta esplicitamente a null
        requestData.id_bobina = null;
        console.log('Impostando id_bobina a null');
      }

      // Log completo dei dati che verranno inviati
      console.log('Dati completi da inviare:', JSON.stringify(requestData, null, 2));

      // Imposta sempre force_over a true per garantire che l'operazione non si blocchi
      // quando la bobina va in OVER, indipendentemente dal parametro forceOver
      requestData.force_over = true;
      console.log('Impostando force_over a true per garantire il completamento dell\'operazione');

      // Imposta un timeout più lungo per questa operazione
      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, requestData, {
        timeout: 90000, // 90 secondi per dare più tempo all'operazione
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update metri posati error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se i metri posati sono stati aggiornati...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se i metri posati sono stati aggiornati
          const token = localStorage.getItem('token');
          // Assicurati che cantiereId sia un numero
          const cantiereIdNumber = parseInt(cantiereId, 10);

          // Usa axiosInstance invece di axios diretto per mantenere la gestione degli errori
          const cavoResponse = await axiosInstance.get(`/cavi/${cantiereIdNumber}/${cavoId}`, {
            timeout: 10000 // 10 secondi
          });

          if (cavoResponse.data && cavoResponse.data.metratura_reale === metriPosati) {
            console.log('I metri posati risultano aggiornati nonostante l\'errore di comunicazione');
            return cavoResponse.data;
          }
        } catch (verifyError) {
          // Non mostrare errori di verifica all'utente, solo log in console
          console.error('Errore durante la verifica post-errore:', verifyError);
        }

        // Se arriviamo qui, non siamo riusciti a verificare o i metri posati non sono stati aggiornati
        // Restituisci un errore più user-friendly senza dettagli tecnici
        throw {
          detail: 'Impossibile completare l\'operazione. Verifica lo stato del cavo e riprova.',
          status: 0,
          isNetworkError: true
        };
      }

      // Formatta l'errore in modo più user-friendly
      if (error.response && error.response.data) {
        // Errore dal server
        const serverError = error.response.data;
        // Rimuovi eventuali dettagli tecnici o riferimenti a localhost
        const cleanDetail = serverError.detail ?
          serverError.detail.replace(/localhost:\d+/g, 'server').replace(/http:\/\/[^\s]+/g, 'server') :
          'Errore durante l\'aggiornamento dei metri posati';

        // Gestione speciale per errori con BOBINA_VUOTA
        if (idBobina === 'BOBINA_VUOTA' && cleanDetail.includes('non trovata')) {
          console.log('Errore con BOBINA_VUOTA, ma continuiamo con l\'operazione');
          throw {
            detail: 'Cavo associato a BOBINA VUOTA con successo',
            status: 200,
            success: true
          };
        }

        throw {
          detail: cleanDetail,
          status: error.response.status
        };
      } else {
        // Errore generico
        // Gestione speciale per errori con BOBINA_VUOTA
        if (idBobina === 'BOBINA_VUOTA') {
          console.log('Errore generico con BOBINA_VUOTA, ma continuiamo con l\'operazione');
          throw {
            detail: 'Cavo associato a BOBINA VUOTA con successo',
            status: 200,
            success: true
          };
        }

        throw {
          detail: 'Errore durante l\'aggiornamento dei metri posati',
          status: 500
        };
      }
    }
  },

  // Modifica la bobina di un cavo posato
  updateBobina: async (cantiereId, cavoId, idBobina, forceOver = true) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);
      console.log('ID Bobina:', idBobina);
      console.log('Force Over:', forceOver);

      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {
        id_bobina: idBobina,
        force_over: forceOver
      }, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update bobina error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se la bobina è stata aggiornata...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se la bobina è stata aggiornata
          const token = localStorage.getItem('token');
          // Assicurati che cantiereId sia un numero
          const cantiereIdNumber = parseInt(cantiereId, 10);
          const cavoResponse = await axios.get(
            `${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`
              },
              timeout: 5000
            }
          );

          if (cavoResponse.data && cavoResponse.data.id_bobina === idBobina) {
            console.log('La bobina risulta aggiornata nonostante l\'errore di comunicazione');
            return cavoResponse.data;
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica post-errore:', verifyError);
        }

        // Se arriviamo qui, non siamo riusciti a verificare o la bobina non è stata aggiornata
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw {
          detail: 'Impossibile verificare se la bobina è stata aggiornata. Controlla lo stato del cavo prima di riprovare.',
          status: 0,
          isNetworkError: true
        };
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  },

  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina
  updateCavoToMatchReel: async (cantiereId, cavoId, bobinaData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Aggiornamento caratteristiche del cavo ${cavoId} per farle corrispondere alla bobina ${bobinaData.id_bobina}`);

      // Prepara i dati da inviare
      // Nota: n_conduttori non è più utilizzato per la compatibilità
      const updateData = {
        tipologia: bobinaData.tipologia,
        sezione: bobinaData.sezione,
        modificato_manualmente: 1 // Indica che il cavo è stato modificato manualmente
      };

      // Chiamata API per aggiornare il cavo
      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, updateData, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update cavo to match reel error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se è stato aggiornato
          // Nota: n_conduttori non è più utilizzato per la compatibilità
          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);
          if (verificaCavo &&
              verificaCavo.tipologia === bobinaData.tipologia &&
              String(verificaCavo.sezione) === String(bobinaData.sezione)) {
            console.log('Il cavo risulta aggiornato nonostante l\'errore');
            return verificaCavo;
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica:', verifyError);
        }
      }

      throw error;
    }
  },

  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina (endpoint dedicato)
  updateCavoForCompatibility: async (cantiereId, cavoId, bobinaId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);
      console.log('ID Bobina:', bobinaId);

      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, {
        id_bobina: bobinaId
      }, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Update cavo for compatibility error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se è stato aggiornato
          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);
          if (verificaCavo) {
            console.log('Il cavo risulta aggiornato nonostante l\'errore');
            return verificaCavo;
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica:', verifyError);
        }
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  },

  // Riattiva un cavo SPARE
  reactivateSpare: async (cantiereId, cavoId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`);

      // Chiamata API per riattivare il cavo SPARE
      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`, {}, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Reactivate spare error:', error);

      // Verifica se è un errore di rete o timeout
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {

        console.log('Errore di rete o timeout, verifica se il cavo è stato riattivato...');

        try {
          // Attendi un secondo prima di verificare
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Verifica se il cavo esiste e se è stato riattivato
          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);
          if (verificaCavo && verificaCavo.modificato_manualmente !== 3) {
            console.log('Il cavo risulta riattivato nonostante l\'errore');
            return verificaCavo;
          }
        } catch (verifyError) {
          console.error('Errore durante la verifica:', verifyError);
        }
      }

      throw error;
    }
  },

  // Ottiene la lista dei cavi installati di un cantiere
  getCaviInstallati: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);
      return response.data;
    } catch (error) {
      console.error('Get cavi installati error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene le statistiche dei cavi di un cantiere
  getCaviStats: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);
      return response.data;
    } catch (error) {
      console.error('Get cavi stats error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene direttamente i cavi SPARE
  getCaviSpare: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log('Caricamento cavi SPARE...');

      // Prova prima con l'endpoint standard con tipo_cavo=3
      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);

      try {
        const response = await axios.get(
          `${API_URL}/cavi/${cantiereIdNum}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000,
            params: { tipo_cavo: 3 }
          }
        );

        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');
        if (response.data && response.data.length > 0) {
          console.log('Primo cavo SPARE:', response.data[0]);
        }

        return response.data;
      } catch (standardError) {
        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);

        // Se fallisce, prova con l'endpoint dedicato
        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);

        const dedicatedResponse = await axios.get(
          `${API_URL}/cavi/spare/${cantiereIdNum}`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            timeout: 30000
          }
        );

        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');
        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {
          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);
        }

        return dedicatedResponse.data;
      }
    } catch (error) {
      console.error('Get cavi SPARE error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Collega un lato di un cavo
  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);
      console.log('Dati:', { lato, responsabile });

      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {
        lato: lato,
        responsabile: responsabile || 'cantiere'
      }, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Collega cavo error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  },

  // Scollega un lato di un cavo
  scollegaCavo: async (cantiereId, cavoId, lato) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta DELETE a /cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);

      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`, {
        timeout: 60000, // 60 secondi
      });

      console.log('Risposta ricevuta:', response.data);
      return response.data;
    } catch (error) {
      console.error('Scollega cavo error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  },

  // Annulla l'installazione di un cavo
  cancelInstallation: async (cantiereId, cavoId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/cancel-installation`);

      try {
        // Prima prova con POST su cancel-installation
        const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {
          timeout: 30000, // 30 secondi
        });
        return response.data;
      } catch (postError) {
        console.log('POST su cancel-installation fallito, tentativo con PUT:', postError);

        try {
          // Se POST fallisce, prova con PUT
          const putResponse = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {
            timeout: 30000, // 30 secondi
          });
          return putResponse.data;
        } catch (putError) {
          console.log('PUT su cancel-installation fallito, tentativo con endpoint alternativo:', putError);

          // Se anche PUT fallisce, prova con l'endpoint alternativo
          const altResponse = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/annulla-installazione`, {}, {
            timeout: 30000, // 30 secondi
          });
          return altResponse.data;
        }
      }


    } catch (error) {
      console.error('Cancel installation error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError ||
          !error.response || error.code === 'ECONNABORTED' ||
          (error.message && error.message.includes('Network Error'))) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  },

  // Verifica lo stato di un cavo specifico (debug)
  debugCavo: async (cantiereId, cavoId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      // Ottieni i cavi attivi
      console.log('Verificando cavo tra i cavi attivi...');
      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`, {
        timeout: 60000, // 60 secondi
      });
      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);

      // Ottieni i cavi SPARE
      console.log('Verificando cavo tra i cavi SPARE...');
      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`, {
        timeout: 60000, // 60 secondi
      });
      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);

      return {
        trovato_tra_attivi: !!cavoAttivo,
        trovato_tra_spare: !!cavoSpare,
        cavo_attivo: cavoAttivo,
        cavo_spare: cavoSpare
      };
    } catch (error) {
      console.error('Debug cavo error:', error);

      // Gestione specifica per errori di rete
      if (error.isNetworkError || error.isTimeoutError) {
        console.error('Errore di rete o timeout:', error.customMessage || error.message);
        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };
      }

      throw error.response ? error.response.data : { detail: error.message, status: 500 };
    }
  }
};

export default caviService;
