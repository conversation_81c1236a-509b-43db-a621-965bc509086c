import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const reportService = {
  // Ottiene il report di avanzamento
  getProgressReport: async (cantiereId, formato = 'video') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get progress report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene la distinta materiali (Bill of Quantities)
  getBillOfQuantities: async (cantiereId, formato = 'pdf') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get bill of quantities error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene il report di utilizzo bobine
  getBobineReport: async (cantiereId, formato = 'pdf') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobine?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get bobine report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene il report di una bobina specifica
  getBobinaReport: async (cantiereId, idBobina, formato = 'pdf') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobina/${idBobina}?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get bobina report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene il report di posa per periodo
  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'pdf') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(
        `/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`
      );
      return response.data;
    } catch (error) {
      console.error('Get posa per periodo report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene il report dei cavi per stato
  getCaviStatoReport: async (cantiereId, formato = 'pdf') => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/cavi-stato?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get cavi stato report error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default reportService;
