/**
 * Utility per la gestione degli stati dei cavi e delle bobine
 * Implementa le stesse regole di transizione della CLI
 */

// Stati del cavo
export const CABLE_STATES = {
  DA_INSTALLARE: 'Da installare',
  IN_CORSO: 'In corso',
  INSTALLATO: 'Installato',
  SPARE: 'SPARE'
};

// Stati della bobina
export const REEL_STATES = {
  DISPONIBILE: 'Disponibile',
  IN_USO: 'In uso',
  TERMINATA: 'Terminata',
  OVER: 'Over'
};

/**
 * Determina lo stato di installazione di un cavo in base ai metri posati
 * @param {number} metriPosati - Metri posati
 * @param {number} metriTeorici - Metri teorici
 * @returns {string} - Stato di installazione
 */
export const determineCableState = (metriPosati, metriTeorici) => {
  if (!metriPosati || parseFloat(metriPosati) <= 0) {
    return CABLE_STATES.DA_INSTALLARE;
  }

  if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {
    return CABLE_STATES.INSTALLATO;
  }

  return CABLE_STATES.IN_CORSO;
};

/**
 * Determina lo stato di una bobina in base ai metri residui e totali
 * @param {number} metriResidui - Metri residui
 * @param {number} metriTotali - Metri totali
 * @returns {string} - Stato della bobina
 */
export const determineReelState = (metriResidui, metriTotali) => {
  if (metriResidui < 0) {
    return REEL_STATES.OVER;
  }

  if (metriResidui === 0) {
    return REEL_STATES.TERMINATA;
  }

  if (metriResidui < metriTotali) {
    return REEL_STATES.IN_USO;
  }

  return REEL_STATES.DISPONIBILE;
};

/**
 * Verifica se un cavo può essere modificato in base al suo stato
 * @param {Object} cavo - Oggetto cavo
 * @returns {boolean} - True se il cavo può essere modificato, false altrimenti
 */
export const canModifyCable = (cavo) => {
  // Un cavo può essere modificato se:
  // 1. È in stato DA_INSTALLARE
  // 2. Non ha metri posati (metratura_reale = 0)
  return cavo.stato_installazione === CABLE_STATES.DA_INSTALLARE && 
         (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) === 0);
};

/**
 * Verifica se un cavo è in stato SPARE
 * @param {Object} cavo - Oggetto cavo
 * @returns {boolean} - True se il cavo è in stato SPARE, false altrimenti
 */
export const isCableSpare = (cavo) => {
  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE;
};

/**
 * Verifica se un cavo è già installato
 * @param {Object} cavo - Oggetto cavo
 * @returns {boolean} - True se il cavo è già installato, false altrimenti
 */
export const isCableInstalled = (cavo) => {
  return cavo.stato_installazione === CABLE_STATES.INSTALLATO || 
         (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0);
};

/**
 * Verifica se una bobina può essere modificata in base al suo stato
 * @param {Object} bobina - Oggetto bobina
 * @returns {boolean} - True se la bobina può essere modificata, false altrimenti
 */
export const canModifyReel = (bobina) => {
  // Una bobina può essere modificata se:
  // 1. È in stato DISPONIBILE
  // 2. Non è in stato TERMINATA o OVER
  return bobina.stato_bobina === REEL_STATES.DISPONIBILE || 
         bobina.stato_bobina === REEL_STATES.IN_USO;
};

/**
 * Ottiene il colore associato a uno stato del cavo
 * @param {string} stato - Stato del cavo
 * @returns {string} - Colore associato allo stato
 */
export const getCableStateColor = (stato) => {
  switch (stato) {
    case CABLE_STATES.INSTALLATO:
      return 'success';
    case CABLE_STATES.IN_CORSO:
      return 'warning';
    case CABLE_STATES.SPARE:
      return 'error';
    case CABLE_STATES.DA_INSTALLARE:
    default:
      return 'default';
  }
};

/**
 * Ottiene il colore associato a uno stato della bobina
 * @param {string} stato - Stato della bobina
 * @returns {string} - Colore associato allo stato
 */
export const getReelStateColor = (stato) => {
  switch (stato) {
    case REEL_STATES.DISPONIBILE:
      return 'success';
    case REEL_STATES.IN_USO:
      return 'primary';
    case REEL_STATES.TERMINATA:
      return 'warning';
    case REEL_STATES.OVER:
      return 'error';
    default:
      return 'default';
  }
};
