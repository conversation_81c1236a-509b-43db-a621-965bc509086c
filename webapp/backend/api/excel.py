from fastapi import APIRout<PERSON>, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from typing import Optional, List, Dict, Any
import os
import tempfile
import shutil
from pathlib import Path
import logging
from datetime import datetime

from webapp.backend.api.deps import get_current_user, get_db
from webapp.backend.schemas.user import User
from webapp.backend.config import settings

# Import the excel_manager module
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from modules.excel_manager import (
    importa_cavi_da_excel,
    importa_parco_bobine_da_excel,
    crea_template_excel,
    crea_template_parco_bobine,
    esporta_cavi_excel,
    esporta_parco_bobine_excel
)

# Create a router for Excel operations
router = APIRouter()

# Create a directory for temporary files if it doesn't exist
TEMP_DIR = Path(settings.STATIC_DIR) / "temp"
TEMP_DIR.mkdir(parents=True, exist_ok=True)
print(f"Directory temporanea creata: {TEMP_DIR}")

# Helper function to create a temporary file
def save_upload_file_temp(upload_file: UploadFile) -> Path:
    try:
        suffix = Path(upload_file.filename).suffix
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix, dir=TEMP_DIR) as temp:
            shutil.copyfileobj(upload_file.file, temp)
            return Path(temp.name)
    finally:
        upload_file.file.close()

# Helper function to get a relative URL for a file
def get_file_url(file_path: Path) -> str:
    relative_path = file_path.relative_to(settings.STATIC_DIR)
    return f"{settings.STATIC_URL}/{relative_path}"

# Endpoint to import cables from Excel
@router.post("/{cantiere_id}/import-cavi", response_model=Dict[str, Any])
async def import_cavi(
    cantiere_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Import cables from an Excel file.
    """
    try:
        # Save the uploaded file to a temporary location
        temp_file = save_upload_file_temp(file)

        # Import the cables
        result = importa_cavi_da_excel(cantiere_id, str(temp_file), non_interattivo=True)

        # Clean up the temporary file - use try/except to handle file in use
        try:
            os.unlink(temp_file)
        except Exception as e:
            logging.warning(f"Could not delete temporary file {temp_file}: {str(e)}")
            # Schedule file for deletion later
            background_tasks.add_task(lambda: os.unlink(temp_file) if os.path.exists(temp_file) else None)

        if result:
            return {"success": True, "message": "Cavi importati con successo"}
        else:
            raise HTTPException(status_code=400, detail="Errore durante l'importazione dei cavi")
    except Exception as e:
        logging.error(f"Error importing cables: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'importazione dei cavi: {str(e)}")

# Endpoint to import reel inventory from Excel
@router.post("/{cantiere_id}/import-parco-bobine", response_model=Dict[str, Any])
async def import_parco_bobine(
    cantiere_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Import reel inventory from an Excel file.
    """
    try:
        # Save the uploaded file to a temporary location
        temp_file = save_upload_file_temp(file)

        # Import the reel inventory
        success, message, _ = importa_parco_bobine_da_excel(str(temp_file), cantiere_id)

        # Clean up the temporary file - use try/except to handle file in use
        try:
            os.unlink(temp_file)
        except Exception as e:
            logging.warning(f"Could not delete temporary file {temp_file}: {str(e)}")
            # Schedule file for deletion later
            background_tasks.add_task(lambda: os.unlink(temp_file) if os.path.exists(temp_file) else None)

        if success:
            return {"success": True, "message": message}
        else:
            raise HTTPException(status_code=400, detail=message)
    except Exception as e:
        logging.error(f"Error importing reel inventory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'importazione del parco bobine: {str(e)}")

# Endpoint to create an Excel template for cables
@router.get("/template-cavi", response_class=FileResponse)
async def create_cavi_template(
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Create an Excel template for cables.
    """
    try:
        # Create the template directly in the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"template_cavi_{timestamp}.xlsx"
        static_path = TEMP_DIR / template_filename

        # Create the template with the specific path
        template_path = crea_template_excel(str(static_path))

        if not template_path or not os.path.exists(template_path):
            raise HTTPException(status_code=500, detail="Errore durante la creazione del template")

        # Serve il file direttamente come download
        return FileResponse(
            path=template_path,
            filename=template_filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error creating cable template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante la creazione del template: {str(e)}")

# Endpoint to create an Excel template for reel inventory
@router.get("/template-parco-bobine", response_class=FileResponse)
async def create_parco_bobine_template(
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Create an Excel template for reel inventory.
    """
    try:
        # Create the template directly in the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"template_parco_bobine_{timestamp}.xlsx"
        static_path = TEMP_DIR / template_filename

        # Create the template with the specific path
        template_path = crea_template_parco_bobine(str(static_path))

        if not template_path or not os.path.exists(template_path):
            raise HTTPException(status_code=500, detail="Errore durante la creazione del template")

        # Serve il file direttamente come download
        return FileResponse(
            path=template_path,
            filename=template_filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error creating reel inventory template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante la creazione del template: {str(e)}")

# Endpoint to export cables to Excel
@router.get("/{cantiere_id}/export-cavi", response_model=Dict[str, Any])
async def export_cavi(
    cantiere_id: int,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Export cables to an Excel file.
    """
    try:
        # Export the cables
        export_path = esporta_cavi_excel(cantiere_id)

        if not export_path or not os.path.exists(export_path):
            raise HTTPException(status_code=500, detail="Errore durante l'esportazione dei cavi")

        # Copy the export to the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        static_path = TEMP_DIR / f"export_cavi_{timestamp}.xlsx"
        shutil.copy(export_path, static_path)

        # Return the URL to the export
        file_url = get_file_url(static_path)
        return {"success": True, "file_url": file_url}
    except Exception as e:
        logging.error(f"Error exporting cables: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'esportazione dei cavi: {str(e)}")

# Endpoint to export reel inventory to Excel
@router.get("/{cantiere_id}/export-parco-bobine", response_model=Dict[str, Any])
async def export_parco_bobine(
    cantiere_id: int,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Export reel inventory to an Excel file.
    """
    try:
        # Export the reel inventory
        export_path = esporta_parco_bobine_excel()

        if not export_path or not os.path.exists(export_path):
            raise HTTPException(status_code=500, detail="Errore durante l'esportazione del parco bobine")

        # Copy the export to the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        static_path = TEMP_DIR / f"export_parco_bobine_{timestamp}.xlsx"
        shutil.copy(export_path, static_path)

        # Return the URL to the export
        file_url = get_file_url(static_path)
        return {"success": True, "file_url": file_url}
    except Exception as e:
        logging.error(f"Error exporting reel inventory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'esportazione del parco bobine: {str(e)}")
