from pydantic import BaseModel, Field
from typing import Optional
from datetime import date

class UserBase(BaseModel):
    """Schema base per gli utenti."""
    username: str
    ruolo: str
    data_scadenza: Optional[date] = None
    abilitato: bool = True

class UserCreate(UserBase):
    """Schema per la creazione di un utente."""
    password: str

class UserUpdate(BaseModel):
    """Schema per l'aggiornamento di un utente."""
    username: Optional[str] = None
    password: Optional[str] = None
    ruolo: Optional[str] = None
    data_scadenza: Optional[date] = None
    abilitato: Optional[bool] = None

class UserInDB(UserBase):
    """Schema per un utente nel database."""
    id_utente: int
    password: Optional[str] = None
    password_plain: Optional[str] = None  # Campo per la password in chiaro
    created_by: Optional[int] = None

    class Config:
        orm_mode = True
