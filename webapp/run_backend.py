# run_backend.py
import subprocess
import sys
import os
import time
import signal
import socket
from pathlib import Path


def is_port_in_use(port):
    """Verifica se una porta è già in uso"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0


def kill_process_on_port(port):
    """Termina il processo che occupa una porta specifica"""
    try:
        if sys.platform == "win32":
            # Versione migliorata per Windows che ottiene il PID corretto
            result = subprocess.run(
                f"netstat -ano | findstr :{port} | findstr LISTENING",
                shell=True,
                capture_output=True,
                text=True
            )

            if result.stdout:
                # Estrai i PID dalle righe di output
                for line in result.stdout.strip().split('\n'):
                    if f":{port}" in line and "LISTENING" in line:
                        parts = line.strip().split()
                        if len(parts) > 4:
                            pid = parts[-1]
                            if pid != "0":
                                print(f"Terminazione processo con PID {pid} sulla porta {port}")
                                try:
                                    kill_result = subprocess.run(f"taskkill /F /PID {pid}", shell=True, capture_output=True, text=True)
                                    if kill_result.returncode == 0:
                                        print(f"Processo con PID {pid} terminato con successo")
                                    else:
                                        print(f"Errore durante la terminazione del processo con PID {pid}: {kill_result.stderr}")
                                except Exception as e:
                                    print(f"Eccezione durante la terminazione del processo con PID {pid}: {e}")
            else:
                print(f"Nessun processo LISTENING trovato sulla porta {port}")
        else:
            subprocess.run(f"lsof -ti:{port} | xargs kill -9", shell=True)

        # Attendi che la porta venga liberata
        for _ in range(10):  # Aumentato a 10 secondi
            if not is_port_in_use(port):
                print(f"Porta {port} liberata con successo")
                return True
            time.sleep(1)

        # Se non siamo riusciti a liberare la porta, proviamo a usare una porta diversa
        if is_port_in_use(port):
            print(f"Impossibile liberare la porta {port} dopo diversi tentativi")
            return False
        return True
    except Exception as e:
        print(f"Errore durante la terminazione del processo sulla porta {port}: {e}")
        return False


def run_fastapi(port=8001, max_port_attempts=5):
    """Avvia il server FastAPI (backend)"""
    # Salva la directory corrente
    original_dir = os.getcwd()
    backend_dir = Path(__file__).parent / "backend"

    # Verifica che la directory del backend esista
    if not backend_dir.exists():
        print(f"Errore: La directory del backend non esiste: {backend_dir}")
        return None, port

    # Verifica che il file main.py esista
    main_py = backend_dir / "main.py"
    if not main_py.exists():
        print(f"Errore: Il file main.py non esiste: {main_py}")
        return None, port

    os.chdir(backend_dir)
    print(f"Avvio del server FastAPI (backend) sulla porta {port}...")
    print(f"Directory: {os.getcwd()}")

    # Prova porte consecutive fino a trovarne una libera
    current_port = port
    port_attempts = 0

    while port_attempts < max_port_attempts:
        # Verifica se la porta è già in uso
        if is_port_in_use(current_port):
            print(f"La porta {current_port} è già in uso. Tentativo di liberare la porta...")
            if kill_process_on_port(current_port):
                # Porta liberata con successo
                break
            else:
                # Se non possiamo liberare la porta, proviamo con la porta successiva
                port_attempts += 1
                current_port = port + port_attempts
                print(f"Impossibile liberare la porta {current_port-1}. Provo con la porta {current_port}")
        else:
            # Porta libera trovata
            break

    # Se abbiamo esaurito tutti i tentativi
    if port_attempts >= max_port_attempts:
        print(f"Impossibile trovare una porta libera dopo {max_port_attempts} tentativi.")
        print("Verifica manualmente i processi in esecuzione.")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None, port

    # Utilizziamo Popen invece di run per non bloccare il thread
    try:
        # Crea un file di log per il backend
        log_file = open("backend_log.txt", "w")

        # Modifica il comando per specificare la porta e aumentare il livello di log
        cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", f"--port={current_port}", "--log-level=debug"]

        print(f"Esecuzione comando: {' '.join(cmd)}")

        if sys.platform == "win32":
            process = subprocess.Popen(
                cmd,
                stdout=log_file,
                stderr=subprocess.STDOUT
            )
        else:
            process = subprocess.Popen(
                cmd,
                stdout=log_file,
                stderr=subprocess.STDOUT
            )

        print(f"Backend avviato sulla porta {current_port}. Log disponibile in backend_log.txt")

        # Attendi un po' per assicurarsi che il server si avvii
        print("Attesa avvio del server backend...")
        server_started = False
        for i in range(15):  # Aumentato a 15 secondi
            time.sleep(1)
            print(f"Attesa: {i+1}/15", end="\r")
            # Verifica se il server è in ascolto sulla porta specificata
            if is_port_in_use(current_port):
                print(f"\nServer backend avviato con successo sulla porta {current_port}!")
                server_started = True
                break

        if not server_started:
            print("\nAttenzione: Il server backend potrebbe non essere stato avviato correttamente.")
            print("Verifica il file di log backend_log.txt per maggiori dettagli.")
            # Leggi e mostra le ultime righe del log
            try:
                with open("backend_log.txt", "r") as f:
                    log_lines = f.readlines()
                    if log_lines:
                        print("\nUltime righe del log:")
                        for line in log_lines[-10:]:  # Mostra le ultime 10 righe
                            print(line.strip())
            except Exception as e:
                print(f"Errore durante la lettura del log: {e}")

        # Ripristina la directory originale
        os.chdir(original_dir)
        return process, current_port
    except Exception as e:
        print(f"Errore durante l'avvio del backend: {e}")
        os.chdir(original_dir)  # Ripristina la directory originale
        return None, port


def signal_handler(sig, frame, processes=None):
    """Gestisce il segnale di interruzione (Ctrl+C)"""
    print("\nTerminazione dei server in corso...")

    # Termina i processi avviati
    if processes:
        for process in processes:
            if process and process.poll() is None:  # Se il processo è ancora in esecuzione
                try:
                    # Invia SIGTERM per una chiusura pulita
                    process.terminate()
                    # Attendi che il processo termini, ma con timeout
                    for _ in range(10):  # Attendi fino a 5 secondi
                        if process.poll() is not None:
                            break
                        time.sleep(0.5)

                    # Se il processo è ancora in esecuzione, forzane la chiusura
                    if process.poll() is None:
                        print("Forzatura della chiusura del processo...")
                        process.kill()
                except Exception as e:
                    print(f"Errore durante la terminazione del processo: {e}")

    # Assicurati che le porte siano liberate
    if is_port_in_use(8001):
        kill_process_on_port(8001)

    sys.exit(0)


if __name__ == "__main__":
    # Configura il gestore del segnale per Ctrl+C
    signal.signal(signal.SIGINT, lambda sig, frame: signal_handler(sig, frame, []))

    print("Percorso dello script:", Path(__file__).parent)
    print("Avvio del backend CMS...")
    print("Backend (FastAPI): http://localhost:8001 (o porta alternativa se occupata)")
    print("Premi Ctrl+C per terminare il server\n")

    # Avvia il backend con un massimo di 5 tentativi di porte diverse
    backend_process, backend_port = run_fastapi(port=8001, max_port_attempts=5)
    if not backend_process:
        print("Errore: Impossibile avviare il backend. Verifica i log per maggiori dettagli.")
        sys.exit(1)

    # Aggiorna il gestore del segnale con i processi avviati
    signal.signal(signal.SIGINT, lambda sig, frame: signal_handler(sig, frame, [backend_process]))

    print("\nBackend CMS avviato con successo!")
    print(f"Backend: http://localhost:{backend_port}")
    print("Premi Ctrl+C per terminare il server")

    # Mantiene il programma in esecuzione
    try:
        while True:
            # Verifica se i processi sono ancora in esecuzione
            backend_running = backend_process and backend_process.poll() is None

            if not backend_running:
                print("Il processo backend è terminato. Uscita in corso...")
                break

            time.sleep(1)
    except KeyboardInterrupt:
        print("\nInterruzione richiesta dall'utente. Terminazione dei processi in corso...")
        signal_handler(None, None, [backend_process])
    finally:
        # Assicurati che i processi vengano terminati anche in caso di altri errori
        signal_handler(None, None, [backend_process])
